# Saday Backend

Backend API for the Saday application.

## Project Structure

- `src/` - Source code
- `tests/` - Automated tests
- `scripts/` - Utility scripts for testing and development
- `docs/` - Comprehensive documentation
  - `dev/` - Development setup and configuration
  - `api/` - API documentation and reference
  - `modules/` - Module-specific documentation

## Development

### Setup

1. Install Rust and Cargo
2. Clone the repository
3. Install required system dependencies:
   - On Debian/Ubuntu:
     ```bash
     sudo apt-get update
     sudo apt-get install build-essential pkg-config libssl-dev
     ```
   - On Fedora:
     ```bash
     sudo dnf install make gcc openssl-devel pkgconf-pkg-config
     ```
   - On Arch:
     ```bash
     sudo pacman -S base-devel openssl pkgconf
     ```
4. Create a `.env` file with the required environment variables (see `.env.example`)
5. Run `cargo build` to build the project

> **Note:**
> If you see errors about missing `cc`, `pkg-config`, or OpenSSL, make sure you have installed the above system dependencies.

### Running the Server

```bash
cargo run
```

### Testing

#### Automated Tests

```bash
# Run all tests
cargo test

# Run specific test
cargo test test_name
```

#### Interactive Authentication Tests

These tests require user interaction to complete:

```bash
# Run signup test
cargo test test_signup_flow -- --ignored --nocapture

# Run signin test
cargo test test_signin_flow -- --ignored --nocapture
```

You can also use the menu-driven scripts:

```bash
# PowerShell (Windows)
.\scripts\run_tests.ps1

# Bash (Unix-like systems)
./scripts/run_tests.sh
```

## Documentation

Comprehensive documentation is available in the `docs` directory:

- **Development Setup**: Installation, configuration, and database setup in `docs/dev/`
- **API Documentation**: Authentication, endpoints, and response formats in `docs/api/`
- **Module Documentation**: Detailed information about each module in `docs/modules/`

For a quick start, see the [Documentation README](./docs/README.md).

## Environment Variables

The application requires the following environment variables:

- `DATABASE_URL` - PostgreSQL connection string
- `JWT_SECRET_KEY` - Secret key for JWT tokens
- `JWT_MAXAGE` - JWT token expiration time in minutes
- `FRONTEND_URL` - URL of the frontend application
- `BACKEND_URL` - URL of the backend API
- `SQLX_OFFLINE` - Set to "true" to use offline mode for SQLx

For testing:
- `TEST_EMAIL` - Email address to use for tests
- `TEST_OTP` - OTP code to use for tests
