use std::sync::Arc;
use axum::{
    body::Body,
    http::{Request, Method, StatusCode},
    Router,
};
use lola_backend::{
    config::Config,
    routes::create_router,
    AppState,
};
use serde_json::Value;
use tower::ServiceExt;

// Load environment variables
pub fn load_env() {
    dotenv::dotenv().ok();
}

// Create a test app state
pub fn create_app_state() -> AppState {
    // Load environment variables
    load_env();

    // Create a database connection
    let database_url = std::env::var("DATABASE_URL")
        .expect("DATABASE_URL must be set");

    // Create a connection pool
    let pool = sqlx::postgres::PgPoolOptions::new()
        .max_connections(5)
        .connect_lazy(&database_url)
        .expect("Failed to connect to Postgres");

    // Create a DBClient with the pool
    let db_client = lola_backend::db::DBClient::new(pool);

    let env = Config {
        database_url,
        jwt_secret: std::env::var("JWT_SECRET_KEY")
            .expect("JWT_SECRET_KEY must be set"),
        jwt_maxage: std::env::var("JWT_MAXAGE")
            .unwrap_or_else(|_| "60".to_string())
            .parse::<i64>()
            .unwrap_or(60),
        port: 8001,
        frontend_url: std::env::var("FRONTEND_URL")
            .unwrap_or_else(|_| "http://localhost:3000".to_string()),
        backend_url: std::env::var("BACKEND_URL")
            .unwrap_or_else(|_| "http://localhost:8001".to_string()),
        sqlx_offline: std::env::var("SQLX_OFFLINE")
            .unwrap_or_else(|_| "false".to_string()),
        otp_expiry_minutes: std::env::var("OTP_EXPIRY_MINUTES").unwrap().parse().unwrap(),
        otp_rate_limit_max: std::env::var("OTP_RATE_LIMIT_MAX").unwrap().parse().unwrap(),
        otp_rate_limit_window_hours: std::env::var("OTP_RATE_LIMIT_HOURS").unwrap().parse().unwrap(),
    };

    AppState {
        env,
        db_client,
    }
}

// Create a router with the app state
pub fn create_test_router() -> Router {
    let app_state = create_app_state();
    create_router(Arc::new(app_state))
}

// Helper function to create a JSON request
pub fn create_json_request(method: Method, uri: &str, json_body: &serde_json::Value) -> Request<Body> {
    Request::builder()
        .method(method)
        .uri(uri)
        .header("Content-Type", "application/json")
        .body(Body::from(json_body.to_string()))
        .unwrap()
}

// Helper function to extract JSON from response
pub async fn extract_json_response(response: axum::response::Response) -> (StatusCode, Value) {
    let status = response.status();
    let bytes = axum::body::to_bytes(response.into_body(), usize::MAX).await.unwrap();
    let json = serde_json::from_slice(&bytes).unwrap();
    (status, json)
}
