#!/bin/bash

# Colors for terminal output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message "${BLUE}" "=== JWT Secret Check ==="

# Check if .env file exists
if [ ! -f .env ]; then
    print_message "${RED}" "Error: .env file not found"
    exit 1
fi

# Extract JWT_SECRET_KEY from .env file
JWT_SECRET=$(grep JWT_SECRET_KEY .env | cut -d '=' -f2)

if [ -z "$JWT_SECRET" ]; then
    print_message "${RED}" "Error: JWT_SECRET_KEY not found in .env file"
    exit 1
fi

# Print information about the JWT secret
print_message "${GREEN}" "JWT secret found in .env file"
print_message "${YELLOW}" "JWT secret length: ${#JWT_SECRET}"
print_message "${YELLOW}" "JWT secret first 5 chars: ${JWT_SECRET:0:5}..."

# Check if the secret is at least 32 characters long (recommended for HS256)
if [ ${#JWT_SECRET} -lt 32 ]; then
    print_message "${RED}" "Warning: JWT secret is less than 32 characters long, which is not recommended for HS256 algorithm"
else
    print_message "${GREEN}" "JWT secret length is good (>= 32 characters)"
fi

print_message "${BLUE}" "=== Check Completed ==="
