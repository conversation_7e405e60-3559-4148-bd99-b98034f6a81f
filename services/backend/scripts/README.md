# Test Scripts

This directory contains scripts for testing the Lola backend application.

## Authentication Test Scripts

### Interactive Tests

These scripts provide an interactive way to test the authentication flow:

- `run_tests.ps1` (PowerShell) - Menu-driven script to run interactive tests
- `run_tests.sh` (Bash) - Menu-driven script to run interactive tests

To run the tests:

**PowerShell (Windows):**
```
.\scripts\run_tests.ps1
```

**Bash (Unix-like systems):**
```
chmod +x scripts/run_tests.sh
./scripts/run_tests.sh
```

### Individual Test Scripts

These scripts test specific API endpoints:

- `test_request_otp.ps1` - Test requesting OTP for a new user
- `test_request_otp_existing.ps1` - Test requesting OTP for an existing user
- `test_resend_otp.ps1` - Test resending OTP
- `test_verify_otp.ps1` - Test verifying OTP for a new user
- `test_verify_otp_existing.ps1` - Test verifying OTP for an existing user

## Database Scripts

- `clear_tables.sql` - SQL script to clear database tables
