$headers = @{
    "Content-Type" = "application/json"
}

# Use the OTP code provided by the user
$otpCode = "494957"

$body = @{
    "email" = "<EMAIL>"
    "otp_code" = $otpCode
    # Name is not required for existing users
} | ConvertTo-Json

Write-Host "Testing /auth/verify-otp endpoint..."
$response = Invoke-RestMethod -Uri "http://localhost:8001/api/auth/verify-otp" -Method Post -Headers $headers -Body $body
$response | ConvertTo-Json

# Save the response for later use
$response | ConvertTo-Json | Out-File -FilePath "verify_otp_response.json"
