# Error Handling

The Saday Backend API uses a consistent approach to error handling. This document describes the error handling mechanism, common error codes, and how to handle errors in your client application.

## Error Response Format

All error responses follow a consistent format:

```json
{
  "status": "error",
  "message": "Error description"
}
```

The `message` field contains a human-readable description of the error.

## HTTP Status Codes

The API uses standard HTTP status codes to indicate the type of error:

| Status Code | Description |
|-------------|-------------|
| 400 | Bad Request - The request was invalid or cannot be served |
| 401 | Unauthorized - Authentication is required and has failed or has not been provided |
| 403 | Forbidden - The authenticated user does not have access to the requested resource |
| 404 | Not Found - The requested resource could not be found |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - An error occurred on the server |

## Common Error Messages

### Authentication Errors

| Status Code | Message | Description |
|-------------|---------|-------------|
| 401 | Authentication required. Please log in. | No authentication token provided |
| 401 | Invalid token | The provided token is invalid or malformed |
| 401 | Token has expired | The provided token has expired |
| 401 | User no longer exists | The user associated with the token no longer exists |

### Authorization Errors

| Status Code | Message | Description |
|-------------|---------|-------------|
| 403 | Permission denied | The user does not have permission to access the resource |
| 403 | Insufficient role | The user's role is insufficient for the requested action |

### Input Validation Errors

| Status Code | Message | Description |
|-------------|---------|-------------|
| 400 | Invalid email format | The provided email is not in a valid format |
| 400 | Name is required | The name field is missing or empty |
| 400 | Invalid OTP code | The provided OTP code is invalid |
| 400 | OTP has expired | The provided OTP code has expired |

### Resource Errors

| Status Code | Message | Description |
|-------------|---------|-------------|
| 404 | User not found | The requested user could not be found |
| 404 | Organization not found | The requested organization could not be found |
| 404 | Member not found | The requested organization member could not be found |
| 404 | Invitation not found | The requested invitation could not be found |

### Rate Limiting Errors

| Status Code | Message | Description |
|-------------|---------|-------------|
| 429 | Too many OTP requests | The client has sent too many OTP requests |
| 429 | Too many requests | The client has sent too many requests in general |

### Server Errors

| Status Code | Message | Description |
|-------------|---------|-------------|
| 500 | An unexpected error occurred | An unexpected error occurred on the server |
| 500 | Database error | An error occurred while accessing the database |
| 500 | Email service error | An error occurred while sending an email |

## Handling Errors in Client Applications

When building a client application that interacts with the Saday Backend API, you should handle errors appropriately:

1. **Check the HTTP status code** to determine the type of error
2. **Parse the error message** from the response body
3. **Display a user-friendly error message** to the user
4. **Take appropriate action** based on the error type

### Example: Handling Authentication Errors

```javascript
async function fetchData(url, token) {
  try {
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      
      if (response.status === 401) {
        // Authentication error - redirect to login
        console.error('Authentication error:', errorData.message);
        redirectToLogin();
        return null;
      }
      
      throw new Error(errorData.message || 'An error occurred');
    }
    
    return await response.json();
  } catch (error) {
    console.error('API error:', error.message);
    showErrorToUser(error.message);
    return null;
  }
}
```

### Example: Handling Input Validation Errors

```javascript
async function submitForm(formData) {
  try {
    const response = await fetch('/api/organizations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(formData)
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      
      if (response.status === 400) {
        // Input validation error - show error next to form field
        console.error('Validation error:', errorData.message);
        showFormError(errorData.message);
        return null;
      }
      
      throw new Error(errorData.message || 'An error occurred');
    }
    
    return await response.json();
  } catch (error) {
    console.error('API error:', error.message);
    showErrorToUser(error.message);
    return null;
  }
}
```

## Error Logging

The Saday Backend logs all errors for debugging and monitoring purposes. If you encounter an error that you believe is a bug, please report it with the following information:

1. The API endpoint you were trying to access
2. The HTTP method you were using
3. The request body (if applicable)
4. The error message and status code you received
5. Any additional context that might be helpful
