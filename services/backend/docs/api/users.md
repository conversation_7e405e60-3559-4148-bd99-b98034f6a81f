# Users API

The Users API provides endpoints for managing user accounts, profiles, and roles.

## Authentication

All Users API endpoints require authentication. The JWT token can be provided in two ways:

1. As a cookie (set automatically after login)
2. In the Authorization header: `Authorization: Bearer YOUR_JWT_TOKEN`

## User Endpoints

### Get Current User

Returns information about the authenticated user.

- **URL**: `/users/me`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions**: Any authenticated user

#### Success Response

```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "9669208d-b352-4258-bf59-e9b4a543fa16",
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "role": "user",
      "verified": true,
      "verified_admin": false,
      "profile_data": {
        "preferences": {
          "theme": "dark",
          "language": "en"
        },
        "bio": "Software developer"
      },
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z"
    }
  }
}
```

### Get All Users

Returns a list of all users. This endpoint is only accessible to administrators.

- **URL**: `/users/users`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions**: Admin role

#### Query Parameters

- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10, max: 50)
- `name`: Filter users by name (optional)
- `email`: Filter users by email (optional)
- `role`: Filter users by role (optional)

#### Success Response

```json
{
  "status": "success",
  "users": [
    {
      "id": "9669208d-b352-4258-bf59-e9b4a543fa16",
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "user",
      "verified": true,
      "verified_admin": false,
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z"
    },
    {
      "id": "9669208d-b352-4258-bf59-e9b4a543fa17",
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "role": "admin",
      "verified": true,
      "verified_admin": true,
      "createdAt": "2023-01-02T00:00:00Z",
      "updatedAt": "2023-01-02T00:00:00Z"
    }
  ],
  "results": 2
}
```

### Update User Profile

Updates the profile information for the authenticated user.

- **URL**: `/users/profile`
- **Method**: `PUT`
- **Auth Required**: Yes
- **Permissions**: Any authenticated user

#### Request Body

```json
{
  "name": "Updated Name",
  "profile_data": {
    "preferences": {
      "theme": "light",
      "language": "fr"
    },
    "bio": "Updated bio",
    "location": "Paris, France"
  }
}
```

#### Success Response

```json
{
  "status": "success",
  "message": "Profile updated successfully",
  "data": {
    "user": {
      "id": "9669208d-b352-4258-bf59-e9b4a543fa16",
      "name": "Updated Name",
      "email": "<EMAIL>",
      "role": "user",
      "verified": true,
      "verified_admin": false,
      "profile_data": {
        "preferences": {
          "theme": "light",
          "language": "fr"
        },
        "bio": "Updated bio",
        "location": "Paris, France"
      },
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-03T00:00:00Z"
    }
  }
}
```

### Update User Role

Updates the role of a user. This endpoint is only accessible to administrators.

- **URL**: `/users/role`
- **Method**: `PUT`
- **Auth Required**: Yes
- **Permissions**: Admin role

#### Request Body

```json
{
  "user_id": "9669208d-b352-4258-bf59-e9b4a543fa16",
  "role": "admin"
}
```

#### Success Response

```json
{
  "status": "success",
  "message": "User role updated successfully",
  "data": {
    "user": {
      "id": "9669208d-b352-4258-bf59-e9b4a543fa16",
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "admin",
      "verified": true,
      "verified_admin": false,
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-03T00:00:00Z"
    }
  }
}
```

## User Profile Data

The user profile data is stored as a JSON object and can include various user-specific information. The structure is flexible and can be customized based on your application's needs.

Example profile data structure:

```json
{
  "preferences": {
    "theme": "dark",
    "language": "en",
    "notifications": {
      "email": true,
      "push": false
    }
  },
  "bio": "Software developer with 5 years of experience",
  "location": "San Francisco, CA",
  "social": {
    "twitter": "@username",
    "github": "username",
    "linkedin": "username"
  }
}
```

## User Roles

The system supports the following user roles:

- **Admin**: Full access to the system, can manage users and settings
- **User**: Standard user with access to their own data and organizations

## Error Handling

The Users API uses the standard error handling mechanism described in the [Error Handling](./errors.md) documentation.

Common errors for the Users API include:

- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: User not found
- `400 Bad Request`: Invalid input data
