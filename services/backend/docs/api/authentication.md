# Authentication API

The Saday Backend uses a passwordless authentication system based on email One-Time Passwords (OTPs). This document provides detailed information about the authentication API endpoints.

## Authentication Flow

1. User requests an OTP by providing their email address
2. System sends an OTP to the user's email
3. User verifies the OTP
4. System generates a JWT token and returns it to the user
5. User includes the JWT token in subsequent requests

## API Endpoints

### Request OTP

Sends an OTP to the specified email address.

- **URL**: `/auth/request-otp`
- **Method**: `POST`
- **Auth Required**: No
- **Rate Limit**: 100 requests per hour

#### Request Body

```json
{
  "email": "<EMAIL>",
  "invite_token": "abc123-def456-ghi789"
}
```

#### Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `email` | string | Yes | Email address to send OTP to |
| `invite_token` | string | No | Organization invitation token (if accepting an invitation) |

#### Success Response

For existing users:

```json
{
  "status": "success",
  "message": "OTP sent to your email",
  "userType": "Existing",
  "invitation": {
    "organization_name": "My Organization",
    "role": "user",
    "invited_by": "<EMAIL>"
  }
}
```

For new users:

```json
{
  "status": "success",
  "message": "OTP sent to your email",
  "userType": "New",
  "signup_text": "Complete your registration to join My Organization",
  "invitation": {
    "organization_name": "My Organization",
    "role": "user",
    "invited_by": "<EMAIL>"
  }
}
```

For users without invitation:

```json
{
  "status": "success",
  "message": "OTP sent to your email",
  "userType": "New",
  "signup_text": "Complete your registration by entering the OTP sent to your email"
}
```

#### Error Responses

Invalid email format:

```json
{
  "status": "error",
  "message": "Invalid email format"
}
```

Rate limit exceeded:

```json
{
  "status": "error",
  "message": "Too many OTP requests. Please try again later."
}
```

Invalid invitation token:

```json
{
  "status": "error",
  "message": "Invalid or expired invitation"
}
```

Invitation not found:

```json
{
  "status": "error",
  "message": "Invitation not found"
}
```

### Verify OTP

Verifies the OTP and authenticates the user.

- **URL**: `/auth/verify-otp`
- **Method**: `POST`
- **Auth Required**: No

#### Request Body

For existing users:

```json
{
  "email": "<EMAIL>",
  "otp_code": "123456",
  "invite_token": "abc123-def456-ghi789"
}
```

For new users:

```json
{
  "email": "<EMAIL>",
  "otp_code": "123456",
  "name": "John Doe",
  "profile_data": {
    "favorite_color": "blue",
    "preferred_language": "english"
  },
  "invite_token": "abc123-def456-ghi789"
}
```

#### Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `email` | string | Yes | Email address |
| `otp_code` | string | Yes | 6-digit OTP code |
| `name` | string | No | User's name (required for new users) |
| `profile_data` | object | No | Additional profile information |
| `invite_token` | string | No | Organization invitation token (if accepting an invitation) |

#### Success Response

For existing users:

```json
{
  "status": "success",
  "token": "jwt_token_here",
  "userType": "Existing"
}
```

For new users:

```json
{
  "status": "success",
  "token": "jwt_token_here",
  "userType": "New",
  "message": "Account created successfully"
}
```

#### Error Responses

Invalid OTP:

```json
{
  "status": "error",
  "message": "Invalid OTP code"
}
```

OTP expired:

```json
{
  "status": "error",
  "message": "OTP has expired. Please request a new one."
}
```

Invalid invitation token:

```json
{
  "status": "error",
  "message": "Invalid or expired invitation"
}
```

Invitation not found:

```json
{
  "status": "error",
  "message": "Invitation not found"
}
```

### Resend OTP

Resends an OTP to the specified email address.

- **URL**: `/auth/resend-otp`
- **Method**: `POST`
- **Auth Required**: No
- **Rate Limit**: 100 requests per hour

#### Request Body

```json
{
  "email": "<EMAIL>"
}
```

#### Success Response

```json
{
  "status": "success",
  "message": "OTP resent to your email",
  "userType": "Existing"
}
```

#### Error Responses

Email not found:

```json
{
  "status": "error",
  "message": "Email not found"
}
```

Rate limit exceeded:

```json
{
  "status": "error",
  "message": "Too many OTP requests. Please try again later."
}
```

### Request Account Reset

Initiates the process to reset an existing user account.

- **URL**: `/auth/reset-account`
- **Method**: `POST`
- **Auth Required**: No
- **Rate Limit**: 100 requests per hour

#### Request Body

```json
{
  "email": "<EMAIL>"
}
```

#### Success Response

```json
{
  "status": "success",
  "message": "OTP sent to your email for account reset"
}
```

#### Error Responses

Email not found:

```json
{
  "status": "error",
  "message": "Email not found"
}
```

### Verify Account Reset

Verifies the OTP and resets the user account.

- **URL**: `/auth/verify-reset`
- **Method**: `POST`
- **Auth Required**: No

#### Request Body

```json
{
  "email": "<EMAIL>",
  "otp_code": "123456"
}
```

#### Success Response

```json
{
  "status": "success",
  "message": "Account has been reset successfully. You can now log in as a new user."
}
```

#### Error Responses

Invalid OTP:

```json
{
  "status": "error",
  "message": "Invalid OTP code"
}
```

## Organization Invitation Flow

The authentication system supports organization invitations, allowing users to join organizations during the signup or login process.

### How Invitation Flow Works

1. **Organization admin/editor sends invitation** via the organization API
2. **Invitee receives email** with invitation link containing a token
3. **User clicks invitation link** and is redirected to the auth page with the token
4. **User requests OTP** with the invitation token included
5. **User verifies OTP** with the invitation token included
6. **User is automatically added** to the organization with the specified role

### Invitation Token Usage

When a user has an invitation token, include it in both the `request-otp` and `verify-otp` calls:

```javascript
// Step 1: Request OTP with invitation token
const otpResponse = await fetch('/api/auth/request-otp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    invite_token: 'abc123-def456-ghi789'
  })
});

// Step 2: Verify OTP with invitation token
const verifyResponse = await fetch('/api/auth/verify-otp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    otp_code: '123456',
    name: 'John Doe', // Required for new users
    invite_token: 'abc123-def456-ghi789'
  })
});
```

### Invitation Context in Responses

When an invitation token is provided, the `request-otp` response includes invitation context:

```json
{
  "status": "success",
  "message": "OTP sent to your email",
  "userType": "New",
  "signup_text": "Complete your registration to join My Organization",
  "invitation": {
    "organization_name": "My Organization",
    "role": "user",
    "invited_by": "<EMAIL>"
  }
}
```

This allows the frontend to display organization-specific messaging and context to the user.

## JWT Token

The JWT token is used for authentication and contains the following claims:

- `sub`: User ID
- `iat`: Issued at timestamp
- `exp`: Expiration timestamp

The token is valid for the duration specified by the `JWT_MAXAGE` environment variable (default: 60 minutes).

## Using the JWT Token

The JWT token can be provided in two ways:

1. As a cookie (set automatically after login)
2. In the Authorization header: `Authorization: Bearer YOUR_JWT_TOKEN`

Example:

```http
GET /api/users/me HTTP/1.1
Host: api.saday.xyz
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyX2lkIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyNDI2MjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

## Error Handling

Authentication errors return appropriate HTTP status codes:

- `400 Bad Request`: Invalid input (e.g., invalid email format)
- `401 Unauthorized`: Authentication failed (e.g., invalid OTP)
- `429 Too Many Requests`: Rate limit exceeded
