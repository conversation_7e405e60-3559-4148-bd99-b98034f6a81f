# API Documentation

This section provides detailed information about the Saday Backend API endpoints, authentication, error handling, and response formats.

## Contents

- [Authentication](./authentication.md) - How authentication works in the API
- [Users API](./users.md) - User management endpoints
- [Organization API](./organization.md) - Organization and team management
- [Error Handling](./errors.md) - Common error codes and how to handle them
- [Response Format](./responses.md) - Standard response formats for the API
- [API Reference](./reference.md) - Detailed documentation for all API endpoints

### User Journey Documentation

- [New User Signup](../user-journeys/new-user-signup.md) - Complete signup flow for new users
- [Organization Invitation](../user-journeys/organization-invitation.md) - Invitation and acceptance process

## Base URL

All API endpoints are prefixed with:

```
https://api.saday.xyz/api
```

## Authentication

Most endpoints require authentication via JWT token. The token can be provided in two ways:

1. As a cookie (set automatically after login)
2. In the Authorization header: `Authorization: Bearer YOUR_JWT_TOKEN`

For more details, see the [Authentication](./authentication.md) documentation.

### Organization Invitations

The authentication system supports organization invitations, allowing users to join organizations during the signup or login process. When a user has an invitation token, it should be included in both the `request-otp` and `verify-otp` calls.

Example with invitation token:

```json
{
  "email": "<EMAIL>",
  "invite_token": "abc123-def456-ghi789"
}
```

For more details, see the [Organization Invitation Journey](../user-journeys/organization-invitation.md) documentation.

## Response Format

All API responses follow a consistent format:

### Success Response

```json
{
  "status": "success",
  "data": { ... } // or message: "Success message"
}
```

### Error Response

```json
{
  "status": "error",
  "message": "Error description"
}
```

For more details, see the [Response Format](./responses.md) documentation.

## Pagination

List endpoints support pagination with the following query parameters:

- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10, max: 50)

## API Endpoints Overview

### Authentication Endpoints

- `POST /auth/request-otp` - Request an OTP for authentication
- `POST /auth/verify-otp` - Verify OTP and authenticate
- `POST /auth/resend-otp` - Resend OTP to email
- `POST /auth/reset-account` - Request account reset
- `POST /auth/verify-reset` - Verify account reset

### User Endpoints

- `GET /users/me` - Get current user information
- `GET /users/users` - Get all users (admin only)
- `PUT /users/profile` - Update user profile
- `PUT /users/role` - Update user role

### Organization Endpoints

- `POST /organizations` - Create a new organization
- `GET /organizations` - Get user organizations
- `GET /organizations/:id` - Get organization details
- `PUT /organizations/:id` - Update organization
- `DELETE /organizations/:id` - Delete organization
- `GET /organizations/:id/members` - Get organization members
- `POST /organizations/:id/members` - Invite member to organization
- `PUT /organizations/:id/members/:member_id` - Update member role
- `DELETE /organizations/:id/members/:member_id` - Remove member from organization
- `POST /organizations/invitations/accept` - Accept organization invitation
- `GET /organizations/invitations` - Get user invitations
- `GET /organizations/:id/invitations` - Get organization invitations
- `PUT /organizations/default/:id` - Set default organization

For detailed information about each endpoint, see the [API Reference](./reference.md) documentation.
