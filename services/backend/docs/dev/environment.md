# Environment Configuration

This guide provides information about the environment variables and configuration settings for the Saday Backend.

## Environment Variables

The Saday Backend uses environment variables for configuration. These variables are loaded from a `.env` file in the project root directory.

### Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `postgres://postgres:postgres@localhost:5432/saday` |
| `JWT_SECRET_KEY` | Secret key for JWT token signing | `your_jwt_secret_key` |
| `JWT_MAXAGE` | JWT token expiration time in minutes | `60` |
| `SMTP_SERVER` | SMTP server for sending emails | `smtp.gmail.com` |
| `SMTP_PORT` | SMTP port | `465` |
| `SMTP_USERNAME` | SMTP username | `<EMAIL>` |
| `SMTP_PASSWORD` | SMTP password | `your_smtp_password` |
| `SMTP_FROM_ADDRESS` | Email address to send from | `<EMAIL>` |
| `FRONTEND_URL` | URL of the frontend application | `http://localhost:3000` |
| `BACKEND_URL` | URL of the backend API | `http://localhost:8001` |

### Optional Environment Variables

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `SMTP_TLS` | Use TLS for SMTP | `true` | `true` |
| `SMTP_SSL` | Use SSL for SMTP | `true` | `true` |
| `SMTP_TLS_REQUIRED` | Require TLS for SMTP | `true` | `true` |
| `SMTP_AUTH_MECHANISM` | SMTP authentication mechanism | `Plain` | `Plain` |
| `OTP_EXPIRY_MINUTES` | OTP expiration time in minutes | `10` | `10` |
| `OTP_MAX_ATTEMPTS` | Maximum number of OTP verification attempts | `5` | `5` |
| `OTP_RATE_LIMIT_HOURS` | Time window in hours for OTP rate limiting | `1` | `1` |
| `OTP_RATE_LIMIT_MAX` | Maximum number of OTP requests allowed per time window | `100` | `100` |
| `RUST_LOG` | Logging level | `info` | `debug` |

## Creating a .env File

1. Create a `.env` file in the project root directory:

   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file with your configuration:

   ```
   # Database (PostgreSQL)
   DATABASE_URL=postgres://postgres:postgres@localhost:5432/saday

   # JSON Web Token Credentials
   JWT_SECRET_KEY=your_jwt_secret_key
   JWT_MAXAGE=60

   # SMTP Server Settings
   SMTP_SERVER=your_smtp_server
   SMTP_PORT=465
   SMTP_USERNAME=your_smtp_username
   SMTP_PASSWORD=your_smtp_password
   SMTP_FROM_ADDRESS=<EMAIL>
   SMTP_TLS=true
   SMTP_SSL=true
   SMTP_TLS_REQUIRED=true
   SMTP_AUTH_MECHANISM=Plain

   # Frontend and Backend URLs
   FRONTEND_URL=http://localhost:3000
   BACKEND_URL=http://localhost:8001

   # OTP settings
   OTP_EXPIRY_MINUTES=10
   OTP_MAX_ATTEMPTS=5
   OTP_RATE_LIMIT_HOURS=1
   OTP_RATE_LIMIT_MAX=100

   # Logging
   RUST_LOG=info
   ```

3. Generate a secure JWT secret key:

   ```bash
   openssl rand -hex 32
   ```

   Copy the output and paste it as the value for `JWT_SECRET_KEY` in your `.env` file.

## Configuration Modules

The Saday Backend uses a modular approach to configuration. The configuration is loaded from environment variables and stored in the following structs:

### Config Struct

The `Config` struct in `src/config.rs` loads and validates the environment variables:

```rust
pub struct Config {
    pub database_url: String,
    pub jwt_secret: String,
    pub jwt_maxage: i64,
    pub smtp_server: String,
    pub smtp_port: u16,
    pub smtp_username: String,
    pub smtp_password: String,
    pub smtp_from_address: String,
    pub smtp_tls: bool,
    pub smtp_ssl: bool,
    pub smtp_tls_required: bool,
    pub smtp_auth_mechanism: String,
    pub frontend_url: String,
    pub backend_url: String,
    pub otp_expiry_minutes: i64,
    pub otp_max_attempts: i32,
    pub otp_rate_limit_hours: i64,
    pub otp_rate_limit_max: i64,
}
```

### AppState Struct

The `AppState` struct in `src/main.rs` holds the application state, including the configuration:

```rust
pub struct AppState {
    pub db_client: DatabaseClient,
    pub env: Config,
}
```

## Environment-Specific Configuration

The Saday Backend supports different configurations for different environments:

### Development Environment

For development, you can use a local PostgreSQL database and a local SMTP server (or a service like Mailtrap for testing emails).

Example `.env` for development:

```
DATABASE_URL=postgres://postgres:postgres@localhost:5432/saday
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:8001
RUST_LOG=debug
```

### Production Environment

For production, you should use a secure PostgreSQL database and a reliable SMTP service.

Example `.env` for production:

```
DATABASE_URL=*************************************************************/saday
FRONTEND_URL=https://saday.xyz
BACKEND_URL=https://api.saday.xyz
RUST_LOG=info
```

## Logging Configuration

The Saday Backend uses the `env_logger` crate for logging. The logging level is controlled by the `RUST_LOG` environment variable.

Possible values for `RUST_LOG`:

- `error`: Only log errors
- `warn`: Log warnings and errors
- `info`: Log info, warnings, and errors (default)
- `debug`: Log debug information, info, warnings, and errors
- `trace`: Log everything

Example:

```
RUST_LOG=debug
```

This will log debug information, which is useful for development but may be too verbose for production.

## Troubleshooting

### Common Issues

1. **Missing Environment Variables**

   If you see an error about missing environment variables, ensure that your `.env` file contains all required variables.

2. **Invalid JWT Secret**

   If you see authentication errors, ensure that your JWT secret is properly set and is at least 32 characters long.

3. **SMTP Configuration Issues**

   If emails are not being sent, check your SMTP configuration. You can use a service like Mailtrap for testing emails in development.
