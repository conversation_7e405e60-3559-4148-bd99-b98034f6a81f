# Database Setup

This guide provides instructions for setting up and configuring the PostgreSQL database for the Saday Backend.

## Database Schema

The Saday Backend uses PostgreSQL as its database. The schema consists of the following tables:

### Users Table

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    role user_role NOT NULL DEFAULT 'user',
    verified BOOLEAN NOT NULL DEFAULT FALSE,
    verified_admin BOOLEAN NOT NULL DEFAULT FALSE,
    email_otp VARCHAR(6),
    email_otp_expires_at TIMESTAMPTZ,
    profile_data JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

### OTP Requests Table

```sql
CREATE TABLE otp_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

### Organizations Table

```sql
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    domain VARCHAR(255),
    settings JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

### Organization Members Table

```sql
CREATE TABLE organization_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role organization_role NOT NULL DEFAULT 'orguser',
    invited_by UUID REFERENCES users(id),
    joined_at TIMESTAMPTZ,
    settings JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(organization_id, user_id)
);
```

### Organization Invitations Table

```sql
CREATE TABLE organization_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    role organization_role NOT NULL DEFAULT 'orguser',
    invited_by UUID NOT NULL REFERENCES users(id),
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(organization_id, email)
);
```

### User Default Organization Table

```sql
CREATE TABLE user_default_organizations (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

## Setting Up PostgreSQL

### Creating the Database

1. Connect to PostgreSQL:

   ```bash
   sudo -u postgres psql
   ```

2. Create a new database:

   ```sql
   CREATE DATABASE saday;
   ```

3. Create a new user (optional):

   ```sql
   CREATE USER saday_user WITH PASSWORD 'your_password';
   ```

4. Grant privileges to the user:

   ```sql
   GRANT ALL PRIVILEGES ON DATABASE saday TO saday_user;
   ```

5. Exit PostgreSQL:

   ```sql
   \q
   ```

### Creating Enum Types

The database uses custom enum types for roles. Connect to the database and create these types:

```sql
\c saday

-- Create user role enum
CREATE TYPE user_role AS ENUM ('admin', 'user');

-- Create organization role enum
CREATE TYPE organization_role AS ENUM ('orgadmin', 'orgeditor', 'orgagent', 'orguser', 'orgviewer');
```

### Creating Tables

Run the SQL statements from the schema section to create the tables.

## Database Configuration

The database connection is configured using the `DATABASE_URL` environment variable in the `.env` file:

```
DATABASE_URL=postgres://username:password@localhost:5432/saday
```

Replace `username`, `password`, and `saday` with your PostgreSQL username, password, and database name.

## Database Migrations

The Saday Backend uses SQL migrations for database schema changes. Migrations are stored in the `migrations` directory.

### Running Migrations

To run migrations, use the following command:

```bash
cargo run --bin migrate
```

This will apply all pending migrations to the database.

### Creating a New Migration

To create a new migration, create a new SQL file in the `migrations` directory with the following naming convention:

```
YYYYMMDD_HHMMSS_description.sql
```

For example:

```
20230101_120000_create_users_table.sql
```

The migration file should contain the SQL statements to apply the schema changes.

## Database Backup and Restore

### Backup

To create a backup of the database:

```bash
pg_dump -U postgres -d saday -f backup.sql
```

### Restore

To restore the database from a backup:

```bash
psql -U postgres -d saday -f backup.sql
```

## Troubleshooting

### Common Issues

1. **Connection Refused**

   If you see a "connection refused" error, ensure that PostgreSQL is running:

   ```bash
   sudo systemctl status postgresql
   ```

   If it's not running, start it:

   ```bash
   sudo systemctl start postgresql
   ```

2. **Authentication Failed**

   If you see an "authentication failed" error, check your database connection string in the `.env` file.

3. **Database Does Not Exist**

   If you see a "database does not exist" error, create the database:

   ```bash
   sudo -u postgres psql -c "CREATE DATABASE saday;"
   ```

4. **Permission Denied**

   If you see a "permission denied" error, grant the necessary privileges to your user:

   ```bash
   sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE saday TO your_username;"
   ```
