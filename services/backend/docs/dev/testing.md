# Testing Guide

This guide provides information about testing the Saday Backend, including running tests, creating new tests, and debugging.

## Test Structure

The Saday Backend uses Rust's built-in testing framework. Tests are organized into the following categories:

1. **Unit Tests**: Test individual functions and methods
2. **Integration Tests**: Test API endpoints and database interactions
3. **End-to-End Tests**: Test complete user flows

## Running Tests

### Running All Tests

To run all tests:

```bash
cargo test
```

### Running Specific Tests

To run tests for a specific module:

```bash
cargo test --test <test_name>
```

For example, to run the authentication tests:

```bash
cargo test --test auth
```

To run a specific test function:

```bash
cargo test <function_name>
```

For example:

```bash
cargo test test_request_otp
```

### Running Tests with Output

To see the output of tests (including println! statements):

```bash
cargo test -- --nocapture
```

### Running Tests with Specific Features

To run tests with specific features:

```bash
cargo test --features <feature_name>
```

## Test Environment

Tests use a separate test database to avoid affecting the development or production database. The test database is configured using environment variables in a `.env.test` file.

Example `.env.test` file:

```
DATABASE_URL=postgres://postgres:postgres@localhost:5432/saday_test
JWT_SECRET_KEY=test_jwt_secret_key
JWT_MAXAGE=60
SMTP_SERVER=localhost
SMTP_PORT=1025
SMTP_USERNAME=test
SMTP_PASSWORD=test
SMTP_FROM_ADDRESS=<EMAIL>
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:8001
```

## Creating New Tests

### Unit Tests

Unit tests are typically placed in the same file as the code they're testing, inside a `tests` module:

```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_function_name() {
        // Test code here
        assert_eq!(function_name(input), expected_output);
    }
}
```

### Integration Tests

Integration tests are placed in the `tests` directory at the root of the project. Each test file should focus on testing a specific module or feature.

Example integration test for authentication:

```rust
use reqwest::Client;
use serde_json::{json, Value};

#[tokio::test]
async fn test_request_otp() {
    // Set up the test client
    let client = Client::new();
    
    // Make a request to the API
    let response = client
        .post("http://localhost:8001/api/auth/request-otp")
        .json(&json!({
            "email": "<EMAIL>"
        }))
        .send()
        .await
        .unwrap();
    
    // Assert the response status
    assert_eq!(response.status(), 200);
    
    // Parse the response body
    let body: Value = response.json().await.unwrap();
    
    // Assert the response content
    assert_eq!(body["status"], "success");
    assert_eq!(body["userType"], "New");
}
```

## Test Utilities

The Saday Backend provides several utility functions for testing in the `tests/utils` directory:

### Database Utilities

Functions for setting up and tearing down the test database:

```rust
async fn setup_test_db() -> DatabaseClient {
    // Set up the test database
    // ...
}

async fn teardown_test_db(client: DatabaseClient) {
    // Clean up the test database
    // ...
}
```

### Authentication Utilities

Functions for authenticating test requests:

```rust
async fn authenticate_user(client: &Client, email: &str) -> String {
    // Request OTP
    // ...
    
    // Verify OTP
    // ...
    
    // Return JWT token
    // ...
}
```

## Test Scripts

The Saday Backend includes several test scripts in the `tests` directory:

### JWT Debug Test

The `jwt_debug.rs` test checks JWT token generation and validation:

```bash
cargo test --test jwt_debug -- --nocapture
```

### Token Debug Script

The `token_debug.sh` script helps debug JWT token issues:

```bash
./tests/token_debug.sh
```

### Organization Test Script

The `organization_test.sh` script tests the organization API endpoints:

```bash
./tests/organization_test.sh
```

### Authentication Debug Script

The `auth_debug.sh` script helps debug authentication issues:

```bash
./tests/auth_debug.sh
```

## Debugging Tests

### Common Issues

1. **Database Connection Issues**

   If tests fail with database connection errors, ensure that:
   
   - PostgreSQL is running
   - The test database exists
   - The `DATABASE_URL` in `.env.test` is correct

2. **Authentication Issues**

   If tests fail with authentication errors, check:
   
   - The JWT secret in `.env.test`
   - The token generation and validation logic

3. **API Endpoint Issues**

   If tests fail with API endpoint errors, verify:
   
   - The endpoint URL is correct
   - The request body is properly formatted
   - The expected response matches the actual response

### Debugging with Logs

To enable debug logs during tests:

```bash
RUST_LOG=debug cargo test -- --nocapture
```

This will show detailed logs, which can help identify issues.

## Continuous Integration

The Saday Backend uses GitHub Actions for continuous integration. The CI pipeline runs all tests on every push and pull request.

The CI configuration is defined in `.github/workflows/ci.yml`.

## Test Coverage

To generate a test coverage report:

1. Install cargo-tarpaulin:

   ```bash
   cargo install cargo-tarpaulin
   ```

2. Run tarpaulin:

   ```bash
   cargo tarpaulin --out Html
   ```

3. Open `tarpaulin-report.html` in a web browser to view the coverage report.
