# Development Setup

This section provides comprehensive information for developers who want to set up and contribute to the Saday Backend.

## Contents

- [Installation Guide](./installation.md) - How to install the required dependencies and set up the development environment
- [Environment Configuration](./environment.md) - Configuration of environment variables and settings
- [Database Setup](./database.md) - Setting up and configuring the PostgreSQL database
- [Testing Guide](./testing.md) - How to run tests and create new test cases

## Quick Start

For a quick development setup, follow these steps:

1. Install Rust and Cargo
   ```bash
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   ```

2. Clone the repository
   ```bash
   git clone https://github.com/yourusername/saday-backend.git
   cd saday-backend
   ```

3. Install required system dependencies:
   ```bash
   # On Debian/Ubuntu
   sudo apt-get update
   sudo apt-get install build-essential pkg-config libssl-dev

   # On Fedora
   sudo dnf install make gcc openssl-devel pkgconf-pkg-config

   # On Arch
   sudo pacman -S base-devel openssl pkgconf
   ```

4. Create a `.env` file with the required environment variables (see [Environment Configuration](./environment.md))

5. Build the project
   ```bash
   cargo build
   ```

6. Run the server
   ```bash
   cargo run
   ```

## Development Workflow

1. Create a new branch for your feature or bug fix
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Make your changes and commit them
   ```bash
   git add .
   git commit -m "Description of your changes"
   ```

3. Run tests to ensure your changes don't break existing functionality
   ```bash
   cargo test
   ```

4. Push your changes to the remote repository
   ```bash
   git push origin feature/your-feature-name
   ```

5. Create a pull request for review

## Troubleshooting

If you encounter any issues during setup, check the following:

- Ensure all required dependencies are installed
- Verify that your `.env` file contains all necessary variables
- Check that the PostgreSQL database is running and accessible
- Ensure you have the correct permissions for the database

For more detailed troubleshooting, refer to the specific documentation sections.
