# New User Signup Journey

This document outlines the complete user journey for a new user signing up for the first time.

## Overview

The Saday platform uses a passwordless authentication system based on email OTP (One-Time Passwords). This provides a secure and user-friendly way for new users to create accounts without the complexity of password management.

The system also supports organization invitations, allowing users to join organizations during the signup process.

## User Journey Types

### Standard Signup
Regular user signup without any organization invitation.

### Invitation-Based Signup
User signup through an organization invitation link.

## Standard User Journey Steps

### 1. User Arrives at Signup Page

- User navigates to the signup/login page
- Single form handles both login and registration
- User enters their email address

### 2. Request OTP

**Frontend Action:**
```javascript
const response = await fetch('/api/auth/request-otp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>'
  })
});
```

**Backend Response:**
```json
{
  "status": "success",
  "message": "OTP sent to your email",
  "userType": "New",
  "signup_text": "Complete your registration by entering the OTP sent to your email"
}
```

**User Experience:**
- User sees confirmation that <PERSON><PERSON> was sent
- User receives email with 6-digit OTP code
- Form updates to show OTP input field and additional signup fields

### 3. User Completes Signup Form

- User enters the 6-digit OTP code from email
- User provides their name
- User optionally provides additional profile information
- User submits the form

### 4. Verify OTP and Create Account

**Frontend Action:**
```javascript
const response = await fetch('/api/auth/verify-otp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    otp_code: '123456',
    name: 'John Doe',
    profile_data: {
      bio: 'Software developer',
      location: 'San Francisco'
    }
  })
});
```

**Backend Response:**
```json
{
  "status": "success",
  "token": "jwt_token_here",
  "userType": "New",
  "message": "Account created successfully"
}
```

**User Experience:**
- Account is created and user is automatically logged in
- JWT token is set as a cookie
- User receives welcome email
- User is redirected to dashboard

## Invitation-Based User Journey Steps

### 1. User Receives Invitation Email

- Organization admin/editor sends invitation via the platform
- User receives email with invitation link
- Email contains organization context and invitation details

### 2. User Clicks Invitation Link

- Link contains invitation token: `https://app.saday.xyz/auth?invite_token=abc123...&email=<EMAIL>`
- Frontend captures the invitation token and email
- User is directed to auth page with invitation context displayed

### 3. Request OTP with Invitation Token

**Frontend Action:**
```javascript
const response = await fetch('/api/auth/request-otp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    invite_token: 'abc123-def456-ghi789'
  })
});
```

**Backend Response:**
```json
{
  "status": "success",
  "message": "OTP sent to your email",
  "userType": "New",
  "signup_text": "Complete your registration to join My Organization",
  "invitation": {
    "organization_name": "My Organization",
    "role": "user",
    "invited_by": "<EMAIL>"
  }
}
```

**User Experience:**
- User sees organization-specific messaging
- User understands they're joining a specific organization
- OTP is sent to their email

### 4. User Completes Signup with Organization Context

- User enters OTP code
- User provides their name and profile information
- Form shows organization context and role information
- User submits the form

### 5. Verify OTP and Join Organization

**Frontend Action:**
```javascript
const response = await fetch('/api/auth/verify-otp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    otp_code: '123456',
    name: 'John Doe',
    profile_data: {
      bio: 'Software developer',
      location: 'San Francisco'
    },
    invite_token: 'abc123-def456-ghi789'
  })
});
```

**Backend Processing:**
1. Verify OTP code
2. Create user account
3. Add user to organization with specified role
4. Mark invitation as accepted
5. Send welcome email with organization context

**User Experience:**
- Account is created and user is logged in
- User is automatically added to the organization
- User receives welcome email mentioning the organization
- User is redirected to organization dashboard

## Error Handling

### Common Errors

1. **Invalid Email Format**
   - Show inline validation error
   - Prevent form submission

2. **Rate Limit Exceeded**
   - Show error message with retry time
   - Disable form temporarily

3. **Invalid OTP Code**
   - Show error message
   - Allow user to retry or request new OTP

4. **Expired OTP**
   - Show error message
   - Automatically trigger new OTP request

5. **Invalid Invitation Token**
   - Show error message
   - Redirect to standard signup flow

### Error Recovery

- All errors provide clear, actionable messages
- Users can easily retry or request new OTP
- Invalid invitations gracefully fall back to standard signup

## Technical Implementation Notes

### Frontend State Management

```javascript
// Invitation context state
const [invitationContext, setInvitationContext] = useState(null);
const [inviteToken, setInviteToken] = useState(null);

// Capture invitation token from URL
useEffect(() => {
  const urlParams = new URLSearchParams(window.location.search);
  const token = urlParams.get('invite_token');
  const email = urlParams.get('email');
  
  if (token) {
    setInviteToken(token);
    // Pre-fill email if provided
    if (email) setEmail(email);
  }
}, []);
```

### Backend Integration

- All auth endpoints support optional `invite_token` parameter
- Invitation validation happens on both OTP request and verification
- Organization membership is automatically created upon successful verification
- Invitation status is tracked and updated appropriately

## Success Metrics

- **Conversion Rate**: Percentage of users who complete signup after starting
- **Invitation Acceptance Rate**: Percentage of invited users who join organizations
- **Time to Complete**: Average time from OTP request to account creation
- **Error Rate**: Percentage of signup attempts that encounter errors

## User Experience Considerations

1. **Clear Messaging**: Users always understand what step they're on
2. **Organization Context**: Invited users see clear organization information
3. **Error Recovery**: All errors provide clear next steps
4. **Mobile Friendly**: Process works seamlessly on mobile devices
5. **Accessibility**: All forms and messages are screen reader friendly
