# Organization Invitation Journey

This document outlines the complete user journey for organization invitations, covering both the invitation process and acceptance flow.

## Overview

The organization invitation system allows organization administrators and editors to invite new members to join their organizations. The system supports inviting both existing users and new users who don't have accounts yet.

## Invitation Process (Admin/Editor Perspective)

### 1. Admin Navigates to Organization Members

- Admin/Editor goes to organization settings or members page
- Clicks "Invite Member" or similar action
- Invitation form is displayed

### 2. Admin Sends Invitation

**Frontend Action:**
```javascript
const response = await fetch(`/api/organizations/${orgId}/members`, {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    role: 'orguser'
  })
});
```

**Backend Response:**
```json
{
  "status": "success",
  "message": "Invitation sent successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "email": "<EMAIL>",
    "role": "orguser",
    "status": "pending",
    "expiresAt": "2023-01-08T00:00:00Z",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

### 3. System Sends Invitation Email

**Email Content:**
```
Subject: You've been invited to join [Organization Name]

Hi there!

You've been invited to join [Organization Name] as a [Role] by [Inviter Name].

To accept this invitation:
1. Click the link below
2. If you don't have an account, you'll be prompted to create one
3. If you already have an account, simply log in

Accept Invitation: https://app.saday.xyz/auth?invite_token=abc123...&email=<EMAIL>

This invitation expires on [Date].

Best regards,
The Saday Team
```

## Invitation Acceptance Journey

### Scenario A: New User (No Account)

#### 1. User Clicks Invitation Link

- User receives email and clicks invitation link
- Link contains: `https://app.saday.xyz/auth?invite_token=abc123...&email=<EMAIL>`
- Frontend captures invitation token and email
- User sees auth page with organization context

#### 2. User Requests OTP

**Frontend displays:**
- Organization name and role information
- "Join [Organization Name] as [Role]" messaging
- Email field (pre-filled from invitation)
- Request OTP button

**API Call:**
```javascript
const response = await fetch('/api/auth/request-otp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    invite_token: 'abc123-def456-ghi789'
  })
});
```

**Response:**
```json
{
  "status": "success",
  "message": "OTP sent to your email",
  "userType": "New",
  "signup_text": "Complete your registration to join My Organization",
  "invitation": {
    "organization_name": "My Organization",
    "role": "orguser",
    "invited_by": "<EMAIL>"
  }
}
```

#### 3. User Completes Signup

- User enters OTP code from email
- User provides name and profile information
- Form shows organization context
- User submits form

**API Call:**
```javascript
const response = await fetch('/api/auth/verify-otp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    otp_code: '123456',
    name: 'John Doe',
    profile_data: {
      bio: 'Software developer'
    },
    invite_token: 'abc123-def456-ghi789'
  })
});
```

#### 4. Account Created and Organization Joined

**Backend Processing:**
1. Verify OTP code
2. Create user account
3. Add user to organization with specified role
4. Mark invitation as "accepted"
5. Send welcome email with organization context

**User Experience:**
- Account created and user logged in
- Automatically becomes member of organization
- Redirected to organization dashboard
- Receives welcome email mentioning organization

### Scenario B: Existing User (Has Account)

#### 1. User Clicks Invitation Link

- Existing user clicks invitation link
- Frontend captures invitation token
- User sees login page with organization context

#### 2. User Logs In

**Frontend displays:**
- "Join [Organization Name] as [Role]" messaging
- Standard login form
- Organization context information

**API Calls:**
```javascript
// Request OTP with invitation token
const otpResponse = await fetch('/api/auth/request-otp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    invite_token: 'abc123-def456-ghi789'
  })
});

// Verify OTP with invitation token
const verifyResponse = await fetch('/api/auth/verify-otp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    otp_code: '123456',
    invite_token: 'abc123-def456-ghi789'
  })
});
```

#### 3. User Joins Organization

**Backend Processing:**
1. Verify OTP code
2. Check if user is already a member
3. Add user to organization (if not already a member)
4. Mark invitation as "accepted"
5. Log user in

**User Experience:**
- User logged in successfully
- Automatically added to organization
- Redirected to organization dashboard
- Sees confirmation of organization membership

## Invitation Management

### Admin View of Invitations

**Get Organization Invitations:**
```javascript
const response = await fetch(`/api/organizations/${orgId}/invitations`, {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

**Response:**
```json
{
  "status": "success",
  "invitations": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "email": "<EMAIL>",
      "role": "orguser",
      "status": "pending",
      "invited_by": "<EMAIL>",
      "expiresAt": "2023-01-08T00:00:00Z",
      "createdAt": "2023-01-01T00:00:00Z"
    }
  ]
}
```

### User View of Invitations

**Get User Invitations:**
```javascript
const response = await fetch('/api/organizations/invitations', {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

## Error Scenarios

### 1. Expired Invitation

**Error Response:**
```json
{
  "status": "error",
  "message": "Invalid or expired invitation"
}
```

**User Experience:**
- Clear error message displayed
- Option to contact organization admin
- Fallback to standard signup/login

### 2. Invalid Invitation Token

**Error Response:**
```json
{
  "status": "error",
  "message": "Invitation not found"
}
```

**User Experience:**
- Error message with explanation
- Link to standard signup/login
- Contact support option

### 3. User Already a Member

**Backend Handling:**
- Check existing membership before adding
- If already a member, just mark invitation as accepted
- Log user in normally

**User Experience:**
- Smooth login experience
- Message indicating they're already a member
- Redirect to organization dashboard

## Technical Implementation

### Database Schema

```sql
CREATE TABLE organization_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    invited_by UUID NOT NULL REFERENCES users(id),
    status VARCHAR(20) DEFAULT 'pending',
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Frontend State Management

```javascript
// Invitation context
const [invitationContext, setInvitationContext] = useState(null);

// URL parameter handling
useEffect(() => {
  const urlParams = new URLSearchParams(window.location.search);
  const inviteToken = urlParams.get('invite_token');
  const email = urlParams.get('email');
  
  if (inviteToken) {
    setInviteToken(inviteToken);
    if (email) setEmail(email);
  }
}, []);

// Display invitation context
{invitationContext && (
  <div className="invitation-banner">
    <h3>Join {invitationContext.organization_name}</h3>
    <p>You've been invited as a {invitationContext.role} by {invitationContext.invited_by}</p>
  </div>
)}
```

## Success Metrics

- **Invitation Send Rate**: Number of invitations sent per organization
- **Invitation Acceptance Rate**: Percentage of invitations accepted
- **Time to Accept**: Average time from invitation to acceptance
- **New User Conversion**: Percentage of invited new users who complete signup
- **Existing User Join Rate**: Percentage of existing users who accept invitations

## Best Practices

1. **Clear Communication**: Invitation emails should clearly explain the organization and role
2. **Expiration Management**: Set reasonable expiration times (7 days recommended)
3. **Role Clarity**: Make it clear what permissions each role has
4. **Follow-up**: Allow admins to resend invitations if needed
5. **Graceful Degradation**: Handle expired/invalid invitations gracefully
