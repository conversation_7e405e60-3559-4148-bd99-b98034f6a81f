# Saday Backend Documentation

Welcome to the Saday Backend documentation. This documentation provides comprehensive information about the backend API, development setup, and module-specific details.

## Documentation Structure

The documentation is organized into the following sections:

### [Development Setup](./dev/README.md)

This section contains information for developers who want to set up and contribute to the Saday Backend:

- [Installation Guide](./dev/installation.md)
- [Environment Configuration](./dev/environment.md)
- [Database Setup](./dev/database.md)
- [Testing Guide](./dev/testing.md)

### [API Documentation](./api/README.md)

This section provides detailed information about the API endpoints:

- [Authentication](./api/authentication.md)
- [Error Handling](./api/errors.md)
- [Response Format](./api/responses.md)
- [API Reference](./api/reference.md)
- [Users API](./api/users.md)
- [Organization API](./api/organization.md)

### [User Journey Documentation](./user-journeys/)

This section provides detailed user flow documentation:

- [New User Signup](./user-journeys/new-user-signup.md)
- [Organization Invitation](./user-journeys/organization-invitation.md)

### [Module Documentation](./modules/README.md)

This section contains detailed documentation for each module:

- [Authentication Module](./modules/auth/README.md)
- [Users Module](./modules/users/README.md)
- [Organization Module](./modules/organization/README.md)
- [Agent Module](./modules/agent/README.md)

## Quick Links

- [Backend Repository](https://github.com/yourusername/saday-backend)
- [Frontend Repository](https://github.com/yourusername/saday-frontend)
- [API Endpoints](./api/reference.md)
- [Database Schema](./dev/database.md)

## Contributing to Documentation

We welcome contributions to improve this documentation. Please follow these guidelines:

1. Use Markdown for all documentation files
2. Follow the existing structure
3. Include code examples where appropriate
4. Keep the documentation up-to-date with code changes
5. Submit a pull request for review

## Support

If you need help or have questions about the documentation, please contact the development <NAME_EMAIL>.
