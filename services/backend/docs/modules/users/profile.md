# User Profile API

## Overview
User profiles in the Lola Backend use a flexible JSONB field `profile_data` that can contain any valid JSON data. This design allows for extensible user profiles without requiring database schema changes.

## Endpoints

### Update User Profile
Updates the user's name and profile data.

- **URL**: `/users/profile`
- **Method**: `PUT`
- **Auth required**: Yes
- **Request Body**:
```json
{
  "name": "<PERSON>",
  "profile_data": {
    "date_of_birth": "1990-01-01",
    "why_lola": "meditation",
    "interests": ["meditation", "journaling", "mindfulness"],
    "preferences": {
      "theme": "dark",
      "notifications": true
    },
    "bio": "I love mindfulness!",
    "location": "San Francisco, CA",
    "social_links": {
      "twitter": "@johndoe",
      "instagram": "@johndoe"
    }
  }
}
```

- **Success Response**:
  - **Code**: 200 OK
  - **Content**:
```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "user_id",
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "role": "user",
      "profile_data": {
        "date_of_birth": "1990-01-01",
        "why_lola": "meditation",
        "interests": ["meditation", "journaling", "mindfulness"],
        "preferences": {
          "theme": "dark",
          "notifications": true
        },
        "bio": "I love mindfulness!",
        "location": "San Francisco, CA",
        "social_links": {
          "twitter": "@johndoe",
          "instagram": "@johndoe"
        }
      },
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z"
    }
  }
}
```

## Profile Data Structure
The `profile_data` field is flexible and can contain any valid JSON data. Here are the commonly used fields:

### Core Fields
- `date_of_birth`: String in YYYY-MM-DD format
- `why_lola`: String ("meditation" | "journaling" | "both")
- `interests`: Array of strings representing user interests
- `bio`: String describing the user
- `location`: String representing user's location

### Preferences
- `preferences`: Object containing user preferences
  - `theme`: String ("light" | "dark")
  - `notifications`: Boolean
  - Additional preferences can be added as needed

### Social Links
- `social_links`: Object containing social media links
  - `twitter`: String (Twitter handle)
  - `instagram`: String (Instagram handle)
  - Additional social platforms can be added as needed

### Activity Preferences
- `meditation_preferences`: Object containing meditation settings
  - `duration`: Number (in minutes)
  - `reminder_time`: String (HH:MM format)
  - `favorite_techniques`: Array of strings

- `journaling_preferences`: Object containing journaling settings
  - `reminder_time`: String (HH:MM format)
  - `template`: String
  - `privacy`: String ("private" | "shared")

Note: All fields in the profile_data object are optional. Clients can store any additional fields they need without requiring backend changes.
