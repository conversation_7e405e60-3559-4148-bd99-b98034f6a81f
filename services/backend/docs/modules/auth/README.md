# Authentication Module

The Authentication module provides a passwordless authentication system based on email One-Time Passwords (OTPs). This approach eliminates the need for users to remember passwords, reduces security risks, and simplifies the user experience.

## Features

- Email OTP-based authentication
- JWT token generation and validation
- User registration and login with the same flow
- Account reset functionality
- Rate limiting for OTP requests

## User Journey

### New User Registration

1. User enters their email address
2. System sends an OTP to the user's email
3. User verifies their email by entering the OTP
4. User provides additional information (name, profile data)
5. Upon successful verification, the user is authenticated and receives a JWT token

### Returning User Authentication

1. User enters their email address
2. System sends an OTP to the user's email
3. User enters the OTP to authenticate
4. Upon successful verification, the user is authenticated and receives a JWT token

### Account Reset

1. User requests an account reset by providing their email
2. System sends an OTP to the user's email
3. User verifies the OTP
4. System resets the user account to a new state
5. User can now log in as a new user

## Email Configuration

The system uses SMTP for email delivery with the following configuration:

- **TLS**: Direct TLS on port 465
- **Configuration Variables**:
  - `SMTP_USERNAME`: SMTP username
  - `SMTP_PASSWORD`: SMTP password
  - `SMTP_SERVER`: SMTP server
  - `SMTP_PORT`: 465 (TLS)
  - `SMTP_FROM_ADDRESS`: Email address to send from

## JWT Token

The JWT token is used for authentication and contains the following claims:

- `sub`: User ID
- `iat`: Issued at timestamp
- `exp`: Expiration timestamp

The token is configured with the following environment variables:

- `JWT_SECRET_KEY`: Secret key for JWT token signing
- `JWT_MAXAGE`: Token expiration time in minutes

## Database Schema

### Users Table

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    role user_role NOT NULL DEFAULT 'user',
    verified BOOLEAN NOT NULL DEFAULT FALSE,
    verified_admin BOOLEAN NOT NULL DEFAULT FALSE,
    email_otp VARCHAR(6),
    email_otp_expires_at TIMESTAMPTZ,
    profile_data JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

### OTP Requests Table

```sql
CREATE TABLE otp_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

## Rate Limiting

To protect the API from abuse, rate limiting is implemented for OTP-related endpoints. The rate limiting settings can be configured through environment variables:

- `OTP_RATE_LIMIT_MAX`: Maximum number of OTP requests allowed per time window (default: 100)
- `OTP_RATE_LIMIT_WINDOW_HOURS`: Time window in hours for rate limiting (default: 1)
- `OTP_EXPIRY_MINUTES`: OTP expiration time in minutes (default: 10)

## API Endpoints

For detailed information about the Authentication API endpoints, see the [Authentication API Reference](../../api/authentication.md).

## Implementation Details

The Authentication module is implemented in the following files:

- `src/handler/auth.rs` - HTTP request handlers
- `src/utils/token.rs` - JWT token generation and validation
- `src/utils/otp.rs` - OTP generation and validation
- `src/mail/mails.rs` - Email sending functionality
- `src/middleware.rs` - Authentication middleware

## Security Considerations

1. **OTP Expiration**: OTPs expire after a configurable time period (default: 10 minutes)
2. **Rate Limiting**: OTP requests are rate-limited to prevent abuse
3. **JWT Token Security**: JWT tokens are signed with a secret key and have an expiration time
4. **Email Security**: Emails are sent over TLS for security

## Troubleshooting

### Common Issues

1. **OTP Not Received**
   - Check spam/junk folder
   - Verify the email address is correct
   - Check if rate limiting is in effect

2. **Invalid OTP**
   - Ensure the OTP is entered correctly
   - Check if the OTP has expired
   - Request a new OTP if needed

3. **JWT Token Issues**
   - Ensure the token is included in the request
   - Check if the token has expired
   - Verify that the token is properly formatted
