-- Drop existing tables if they exist
DROP TABLE IF EXISTS otp_requests;
DROP TABLE IF EXISTS users;

-- Drop the enum type if it exists
DROP TYPE IF EXISTS user_role;

-- Create the user_role enum type
CREATE TYPE user_role AS ENUM ('user', 'admin');

-- Create the users table with only essential fields
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHA<PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    role user_role NOT NULL DEFAULT 'user',
    date_of_birth DATE,
    why_lola VARCHAR(20) CHECK (why_lola IN ('meditation', 'journaling', 'both')),
    email_otp VARCHAR(6),
    email_otp_expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create the otp_requests table for rate limiting
CREATE TABLE otp_requests (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45),
    request_time TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
