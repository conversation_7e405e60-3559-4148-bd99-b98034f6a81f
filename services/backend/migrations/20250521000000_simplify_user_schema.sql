-- Revert previous changes
ALTER TABLE users 
    DROP COLUMN IF EXISTS why_lola,
    DROP COLUMN IF EXISTS date_of_birth,
    DROP COLUMN IF EXISTS user_fields;

DROP INDEX IF EXISTS idx_users_user_fields;

-- Add new profile_data column
ALTER TABLE users ADD COLUMN profile_data JSONB DEFAULT '{}'::jsonb;

-- Create a GIN index for efficient querying of profile_data
CREATE INDEX idx_users_profile_data ON users USING GIN (profile_data);

-- Add comments for documentation
COMMENT ON COLUMN users.profile_data IS 'Flexible JSON field for storing all user profile related data';

-- The users table will now have:
-- id UUID (PK)
-- name VA<PERSON><PERSON><PERSON>(255)
-- email VARCHAR(255) (unique)
-- role user_role
-- email_otp VARCHAR(6)
-- email_otp_expires_at TIMESTAMPTZ
-- profile_data JSONB
-- created_at TIMESTAMPTZ
-- updated_at TIMESTAMPTZ
