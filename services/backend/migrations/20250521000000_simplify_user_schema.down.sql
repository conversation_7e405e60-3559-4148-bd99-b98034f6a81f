-- Remove the new profile_data column
DROP INDEX IF EXISTS idx_users_profile_data;
ALTER TABLE users DROP COLUMN IF EXISTS profile_data;

-- Restore previous columns
ALTER TABLE users 
    ADD COLUMN why_lola VARCHAR(20) CHECK (why_lola IN ('meditation', 'journaling', 'both')),
    ADD COLUMN date_of_birth DATE,
    ADD COLUMN user_fields JSONB DEFAULT '{}'::jsonb;

-- Restore previous index
CREATE INDEX idx_users_user_fields ON users USING GIN (user_fields);
