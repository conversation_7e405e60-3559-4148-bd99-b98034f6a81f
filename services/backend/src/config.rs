#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Config {
    pub database_url: String,
    pub jwt_secret: String,
    pub jwt_maxage: i64,
    pub port: u16,
    pub frontend_url: String,
    pub backend_url: String,
    pub sqlx_offline: String,
    // OTP and rate limiting settings
    pub otp_expiry_minutes: i64,
    pub otp_rate_limit_max: i64,
    pub otp_rate_limit_window_hours: i64,
}

impl Config {

    pub fn init() -> Config {
        let database_url = std::env::var("DATABASE_URL").expect("DATABASE_URL must be set");
        let jwt_secret = std::env::var("JWT_SECRET_KEY").expect("JWT_SECRET_KEY must be set");
        let jwt_maxage = std::env::var("JWT_MAXAGE").expect("JWT_MAXAGE must be set");
        let frontend_url = std::env::var("FRONTEND_URL")
            .unwrap_or_else(|_| "http://localhost:3000".to_string());
        let backend_url = std::env::var("BACKEND_URL")
            .unwrap_or_else(|_| "http://localhost:8001".to_string());
        let sqlx_offline = std::env::var("SQLX_OFFLINE").unwrap_or_else(|_| "false".to_string());

        // OTP and rate limiting settings with defaults
        let otp_expiry_minutes = std::env::var("OTP_EXPIRY_MINUTES")
            .unwrap_or_else(|_| "10".to_string())
            .parse::<i64>()
            .unwrap_or(10);

        let otp_rate_limit_max = std::env::var("OTP_RATE_LIMIT_MAX")
            .unwrap_or_else(|_| "100".to_string())
            .parse::<i64>()
            .unwrap_or(100);

        let otp_rate_limit_window_hours = std::env::var("OTP_RATE_LIMIT_WINDOW_HOURS")
            .unwrap_or_else(|_| "1".to_string())
            .parse::<i64>()
            .unwrap_or(1);

        Config {
            database_url,
            jwt_secret,
            jwt_maxage: jwt_maxage.parse::<i64>().unwrap(),
            port: 8001,
            frontend_url,
            backend_url,
            sqlx_offline,
            otp_expiry_minutes,
            otp_rate_limit_max,
            otp_rate_limit_window_hours,
        }
    }

}
