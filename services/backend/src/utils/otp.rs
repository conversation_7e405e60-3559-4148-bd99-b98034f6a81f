use base64::{engine::general_purpose, Engine as _};
use image::{<PERSON><PERSON><PERSON><PERSON>, ImageBuffer, Rg<PERSON>};
use qrcode::QrCode;
use std::io::Cursor;
use totp_rs::{Algorithm, TOTP};
use rand::Rng;

// Constants for TOTP configuration
const ISSUER: &str = "Lola";
const DIGITS: usize = 6;
const STEP: u64 = 30;
const ALGORITHM: Algorithm = Algorithm::SHA1;

// Generate a random 6-digit OTP code
pub fn generate_email_otp() -> String {
    let mut rng = rand::thread_rng();
    let otp: u32 = rng.gen_range(100000..=999999);
    otp.to_string()
}

// Generate a new OTP secret
pub fn generate_secret() -> String {
    // Generate 32 random bytes for the secret
    let mut secret = vec![0u8; 32];
    getrandom::getrandom(&mut secret).expect("Failed to generate random bytes");
    base32::encode(base32::Alphabet::RFC4648 { padding: false }, &secret)
}

// Create a TOTP instance from a secret
pub fn create_totp(secret: &str, _username: &str) -> Result<TOTP, String> {
    // Decode the base32 secret
    let secret_bytes = base32::decode(base32::Alphabet::RFC4648 { padding: false }, secret)
        .ok_or_else(|| "Invalid secret format".to_string())?;

    // Create TOTP
    TOTP::new(
        ALGORITHM,
        DIGITS,
        1,
        STEP,
        secret_bytes,
    )
    .map_err(|e| e.to_string())
}

// Generate a QR code for the OTP setup
pub fn generate_qr_code(totp: &TOTP, username: &str) -> Result<String, String> {
    // Generate the provisioning URI
    let url = format!("otpauth://totp/{}:{}?secret={}&issuer={}&algorithm={}&digits={}&period={}",
        ISSUER, username, totp.get_secret_base32(), ISSUER, ALGORITHM.to_string(), DIGITS, STEP);

    // Create QR code
    let code = QrCode::new(url).map_err(|e| e.to_string())?;

    // Convert to image
    let image = code.render::<Rgba<u8>>().build();
    let width = image.width();
    let height = image.height();

    // Convert to DynamicImage
    let dynamic_image = DynamicImage::ImageRgba8(
        ImageBuffer::<Rgba<u8>, Vec<u8>>::from_raw(width, height, image.into_raw())
            .expect("Failed to create image buffer"),
    );

    // Convert to PNG
    let mut buffer = Vec::new();
    let mut cursor = Cursor::new(&mut buffer);
    dynamic_image
        .write_to(&mut cursor, image::ImageOutputFormat::Png)
        .map_err(|e| e.to_string())?;

    // Convert to base64
    let base64_image = general_purpose::STANDARD.encode(&buffer);
    let data_url = format!("data:image/png;base64,{}", base64_image);

    Ok(data_url)
}

// Verify an OTP code
pub fn verify_otp(secret: &str, username: &str, code: &str) -> Result<bool, String> {
    let totp = create_totp(secret, username)?;
    Ok(totp.check_current(code).unwrap_or(false))
}
