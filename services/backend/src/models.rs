use chrono::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Debug, Deserialize, Serialize, <PERSON>lone, Copy, sqlx::Type, PartialEq)]
#[sqlx(type_name = "user_role", rename_all = "lowercase")]
pub enum UserRole {
    Admin,
    User
}

impl UserRole {
    pub fn to_str(&self) -> &str {
        match self {
            UserRole::Admin => "admin",
            UserRole::User => "user",
        }
    }
}

#[derive(Debug, Deserialize, Serialize, Clone, Copy, sqlx::Type, PartialEq)]
#[sqlx(type_name = "organization_role")]
pub enum OrganizationRole {
    #[sqlx(rename = "admin")]
    OrgAdmin,
    #[sqlx(rename = "editor")]
    OrgEditor,
    #[sqlx(rename = "agent")]
    OrgAgent,
    #[sqlx(rename = "user")]
    Org<PERSON><PERSON>,
    #[sqlx(rename = "viewer")]
    OrgViewer
}

impl OrganizationRole {
    pub fn to_str(&self) -> &str {
        match self {
            OrganizationRole::OrgAdmin => "admin",
            OrganizationRole::OrgEditor => "editor",
            OrganizationRole::OrgAgent => "agent",
            OrganizationRole::OrgUser => "user",
            OrganizationRole::OrgViewer => "viewer",
        }
    }

    pub fn from_str(role_str: &str) -> Result<Self, String> {
        match role_str.to_lowercase().as_str() {
            "admin" => Ok(OrganizationRole::OrgAdmin),
            "editor" => Ok(OrganizationRole::OrgEditor),
            "agent" => Ok(OrganizationRole::OrgAgent),
            "user" => Ok(OrganizationRole::OrgUser),
            "viewer" => Ok(OrganizationRole::OrgViewer),
            _ => Err(format!("Invalid organization role: {}", role_str)),
        }
    }
}

#[derive(Debug, Deserialize, Serialize, sqlx::FromRow, sqlx::Type, Clone)]
pub struct User {
    pub id: uuid::Uuid,
    pub name: String,
    pub email: String,
    pub role: UserRole,
    pub email_otp: Option<String>,
    pub email_otp_expires_at: Option<DateTime<Utc>>,
    pub profile_data: Option<serde_json::Value>,
    pub default_organization_id: Option<uuid::Uuid>,
    #[serde(rename = "createdAt")]
    pub created_at: Option<DateTime<Utc>>,
    #[serde(rename = "updatedAt")]
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize, Serialize, sqlx::FromRow, Clone)]
pub struct Organization {
    pub id: uuid::Uuid,
    pub name: String,
    pub description: Option<String>,
    pub domain: Option<String>,
    pub settings: Option<serde_json::Value>,
    #[serde(rename = "createdAt")]
    pub created_at: Option<DateTime<Utc>>,
    #[serde(rename = "updatedAt")]
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize, Serialize, sqlx::FromRow, Clone)]
pub struct OrganizationMember {
    pub id: uuid::Uuid,
    pub organization_id: uuid::Uuid,
    pub user_id: uuid::Uuid,
    pub role: OrganizationRole,
    pub invited_by: Option<uuid::Uuid>,
    pub joined_at: Option<DateTime<Utc>>,
    pub settings: Option<serde_json::Value>,
    #[serde(rename = "createdAt")]
    pub created_at: Option<DateTime<Utc>>,
    #[serde(rename = "updatedAt")]
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize, Serialize, sqlx::FromRow, Clone)]
pub struct OrganizationInvitation {
    pub id: uuid::Uuid,
    pub organization_id: uuid::Uuid,
    pub email: String,
    pub role: OrganizationRole,
    pub invited_by: uuid::Uuid,
    pub token: String,
    pub expires_at: DateTime<Utc>,
    pub status: String,
    #[serde(rename = "createdAt")]
    pub created_at: Option<DateTime<Utc>>,
    #[serde(rename = "updatedAt")]
    pub updated_at: Option<DateTime<Utc>>,
}

impl OrganizationInvitation {
    pub fn is_expired(&self) -> bool {
        Utc::now() > self.expires_at || self.status != "pending"
    }
}