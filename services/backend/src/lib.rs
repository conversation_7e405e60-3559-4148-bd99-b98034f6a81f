pub mod models;
pub mod config;
pub mod dtos;
pub mod error;
pub mod db;
pub mod utils;
pub mod middleware;
pub mod mail;
pub mod handler;
pub mod routes;

use std::sync::Arc;

use axum::http::{header::{ACCEPT, AUTHORIZATION, CACHE_CONTROL, CONTENT_TYPE}, HeaderValue, Method};
use config::Config;
use db::DBClient;
use routes::create_router;
use tower_http::cors::CorsLayer;

#[derive(Debug, Clone)]
pub struct AppState {
    pub env: Config,
    pub db_client: DBClient,
}

// Re-export the token module for tests
pub use utils::token;
