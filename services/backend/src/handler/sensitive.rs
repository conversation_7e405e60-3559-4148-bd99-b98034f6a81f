use std::sync::Arc;

use axum::{response::IntoResponse, routing::get, Extension, Json, Router};

use crate::{dtos::Response, middleware::JWTAuthMiddeware, AppState};

pub fn sensitive_operations_handler() -> Router {
    Router::new()
        .route("/status", get(get_sensitive_status))
}

async fn get_sensitive_status(
    Extension(_app_state): Extension<Arc<AppState>>,
    Extension(user): Extension<JWTAuthMiddeware>
) -> impl IntoResponse {
    // This handler is protected by both auth and admin_verified_check middleware
    // So we know the user is authenticated and verified
    
    let response = Response {
        status: "success",
        message: format!("Sensitive operation accessed by verified admin: {}", user.user.name),
    };

    <PERSON><PERSON>(response)
}