use std::{env, fs};
use lettre::{
    message::{header, SinglePart},
    transport::smtp::{authentication::Credentials, client::TlsParameters},
    Message, SmtpTransport, Transport,
};

pub async fn send_email(
    to_email: &str,
    subject: &str,
    template_path: &str,
    placeholders: &[(String, String)]
) -> Result<(), Box<dyn std::error::Error>> {
    let smtp_username = env::var("SMTP_USERNAME")?;
    let smtp_password = env::var("SMTP_PASSWORD")?;
    let smtp_server = env::var("SMTP_SERVER")?;
    let smtp_port: u16 = env::var("SMTP_PORT")?.parse()?;

    let mut html_template = fs::read_to_string(template_path)?;

    for (key, value) in placeholders {
        html_template = html_template.replace(key, value)
    }

    let email = Message::builder()
        .from(smtp_username.parse()?)
        .to(to_email.parse()?)
        .subject(subject)
        .header(header::ContentType::TEXT_HTML)
        .singlepart(SinglePart::builder()
            .header(header::ContentType::TEXT_HTML)
            .body(html_template)
        )?;

    let creds = Credentials::new(smtp_username.clone(), smtp_password.clone());
    
    let tls_parameters = TlsParameters::new(smtp_server.clone())?;
    let mailer = SmtpTransport::relay(&smtp_server)?
        .credentials(creds)
        .port(smtp_port)
        .tls(lettre::transport::smtp::client::Tls::Wrapper(tls_parameters))
        .build();

    let result = mailer.send(&email);

    match result {
        Ok(_) => println!("Email sent successfully!"),
        Err(e) => println!("Failed to send email: {:?}", e),
    }

    Ok(())
}

pub async fn send_email_with_logos(
    to_email: &str,
    subject: &str,
    template_path: &str,
    placeholders: &[(String, String)],
    _logo_black_path: &str,
    _logo_white_path: &str
) -> Result<(), Box<dyn std::error::Error>> {
    // For now, we'll use the regular send_email function
    // This is a temporary solution until we properly implement logo embedding
    println!("Note: Using simplified email without logos");
    send_email(to_email, subject, template_path, placeholders).await
}