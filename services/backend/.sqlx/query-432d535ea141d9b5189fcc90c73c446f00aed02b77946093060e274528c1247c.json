{"db_name": "PostgreSQL", "query": "\n            SELECT\n                i.id as \"invitation_id!\", i.organization_id, i.email, i.role as \"invitation_role!: OrganizationRole\",\n                i.invited_by, i.token, i.expires_at, i.status, i.created_at as \"invitation_created_at!\", i.updated_at as \"invitation_updated_at!\",\n                o.id as \"org_id!\", o.name as \"org_name!\", o.description, o.domain, o.settings,\n                o.created_at as \"org_created_at!\", o.updated_at as \"org_updated_at!\",\n                u.id as \"user_id!\", u.name as \"user_name!\", u.email as \"user_email!\", u.role as \"user_role!: UserRole\",\n                u.email_otp, u.email_otp_expires_at, u.profile_data, u.default_organization_id,\n                u.created_at as \"user_created_at!\", u.updated_at as \"user_updated_at!\"\n            FROM organization_invitations i\n            JOIN organizations o ON i.organization_id = o.id\n            JOIN users u ON i.invited_by = u.id\n            WHERE i.token = $1 AND i.expires_at > NOW()\n            ", "describe": {"columns": [{"ordinal": 0, "name": "invitation_id!", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 2, "name": "email", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "invitation_role!: OrganizationRole", "type_info": {"Custom": {"name": "organization_role", "kind": {"Enum": ["admin", "editor", "agent", "user", "viewer"]}}}}, {"ordinal": 4, "name": "invited_by", "type_info": "<PERSON><PERSON>"}, {"ordinal": 5, "name": "token", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 6, "name": "expires_at", "type_info": "Timestamptz"}, {"ordinal": 7, "name": "status", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 8, "name": "invitation_created_at!", "type_info": "Timestamptz"}, {"ordinal": 9, "name": "invitation_updated_at!", "type_info": "Timestamptz"}, {"ordinal": 10, "name": "org_id!", "type_info": "<PERSON><PERSON>"}, {"ordinal": 11, "name": "org_name!", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 12, "name": "description", "type_info": "Text"}, {"ordinal": 13, "name": "domain", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 14, "name": "settings", "type_info": "Jsonb"}, {"ordinal": 15, "name": "org_created_at!", "type_info": "Timestamptz"}, {"ordinal": 16, "name": "org_updated_at!", "type_info": "Timestamptz"}, {"ordinal": 17, "name": "user_id!", "type_info": "<PERSON><PERSON>"}, {"ordinal": 18, "name": "user_name!", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 19, "name": "user_email!", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 20, "name": "user_role!: UserRole", "type_info": {"Custom": {"name": "user_role", "kind": {"Enum": ["user", "admin"]}}}}, {"ordinal": 21, "name": "email_otp", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 22, "name": "email_otp_expires_at", "type_info": "Timestamptz"}, {"ordinal": 23, "name": "profile_data", "type_info": "Jsonb"}, {"ordinal": 24, "name": "default_organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 25, "name": "user_created_at!", "type_info": "Timestamptz"}, {"ordinal": 26, "name": "user_updated_at!", "type_info": "Timestamptz"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false, false, false, false, false, false, false, false, false, false, true, true, true, false, false, false, false, false, false, true, true, true, true, false, false]}, "hash": "432d535ea141d9b5189fcc90c73c446f00aed02b77946093060e274528c1247c"}