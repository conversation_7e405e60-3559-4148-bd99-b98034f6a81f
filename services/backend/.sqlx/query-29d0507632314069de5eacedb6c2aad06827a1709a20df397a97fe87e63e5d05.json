{"db_name": "PostgreSQL", "query": "SELECT id, name, email, role as \"role: UserR<PERSON>\", email_otp, email_otp_expires_at, profile_data, default_organization_id, created_at, updated_at FROM users\n            ORDER BY created_at DESC LIMIT $1 OFFSET $2", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "email", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "role: UserRole", "type_info": {"Custom": {"name": "user_role", "kind": {"Enum": ["user", "admin"]}}}}, {"ordinal": 4, "name": "email_otp", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "email_otp_expires_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "profile_data", "type_info": "Jsonb"}, {"ordinal": 7, "name": "default_organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 8, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 9, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["Int8", "Int8"]}, "nullable": [false, false, false, false, true, true, true, true, false, false]}, "hash": "29d0507632314069de5eacedb6c2aad06827a1709a20df397a97fe87e63e5d05"}