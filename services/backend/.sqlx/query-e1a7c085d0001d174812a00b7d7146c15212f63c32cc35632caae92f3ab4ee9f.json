{"db_name": "PostgreSQL", "query": "\n            SELECT id, organization_id, user_id, role as \"role: OrganizationRole\", invited_by, joined_at, settings, created_at, updated_at\n            FROM organization_members\n            WHERE id = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 2, "name": "user_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 3, "name": "role: OrganizationRole", "type_info": {"Custom": {"name": "organization_role", "kind": {"Enum": ["admin", "editor", "agent", "user", "viewer"]}}}}, {"ordinal": 4, "name": "invited_by", "type_info": "<PERSON><PERSON>"}, {"ordinal": 5, "name": "joined_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "settings", "type_info": "Jsonb"}, {"ordinal": 7, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 8, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON>"]}, "nullable": [false, false, false, false, true, false, true, false, false]}, "hash": "e1a7c085d0001d174812a00b7d7146c15212f63c32cc35632caae92f3ab4ee9f"}