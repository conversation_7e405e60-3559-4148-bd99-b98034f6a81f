{"db_name": "PostgreSQL", "query": "\n            UPDATE users\n            SET name = $1, updated_at = Now()\n            WHERE id = $2\n            RETURNING id, name, email, role as \"role: UserRole\", email_otp, email_otp_expires_at, profile_data, default_organization_id, created_at, updated_at\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "email", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "role: UserRole", "type_info": {"Custom": {"name": "user_role", "kind": {"Enum": ["user", "admin"]}}}}, {"ordinal": 4, "name": "email_otp", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "email_otp_expires_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "profile_data", "type_info": "Jsonb"}, {"ordinal": 7, "name": "default_organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 8, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 9, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"]}, "nullable": [false, false, false, false, true, true, true, true, false, false]}, "hash": "60b068e3a339ee3348356d9c5de1aa37c56e6833443529133b8316acfd2ec1cf"}