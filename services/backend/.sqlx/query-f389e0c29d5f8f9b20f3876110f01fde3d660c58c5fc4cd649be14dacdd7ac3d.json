{"db_name": "PostgreSQL", "query": "\n            INSERT INTO organization_invitations (organization_id, email, role, invited_by, token, expires_at, status)\n            VALUES ($1, $2, $3, $4, $5, $6, 'pending')\n            RETURNING id, organization_id, email, role as \"role: OrganizationRole\", invited_by, token, expires_at, status, created_at, updated_at\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 2, "name": "email", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "role: OrganizationRole", "type_info": {"Custom": {"name": "organization_role", "kind": {"Enum": ["admin", "editor", "agent", "user", "viewer"]}}}}, {"ordinal": 4, "name": "invited_by", "type_info": "<PERSON><PERSON>"}, {"ordinal": 5, "name": "token", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 6, "name": "expires_at", "type_info": "Timestamptz"}, {"ordinal": 7, "name": "status", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 8, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 9, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", {"Custom": {"name": "organization_role", "kind": {"Enum": ["admin", "editor", "agent", "user", "viewer"]}}}, "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Timestamptz"]}, "nullable": [false, false, false, false, false, false, false, false, false, false]}, "hash": "f389e0c29d5f8f9b20f3876110f01fde3d660c58c5fc4cd649be14dacdd7ac3d"}