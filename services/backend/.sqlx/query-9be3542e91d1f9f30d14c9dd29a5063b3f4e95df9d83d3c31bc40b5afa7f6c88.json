{"db_name": "PostgreSQL", "query": "\n            SELECT COUNT(*)\n            FROM organizations o\n            JOIN organization_members om ON o.id = om.organization_id\n            WHERE om.user_id = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "count", "type_info": "Int8"}], "parameters": {"Left": ["<PERSON><PERSON>"]}, "nullable": [null]}, "hash": "9be3542e91d1f9f30d14c9dd29a5063b3f4e95df9d83d3c31bc40b5afa7f6c88"}