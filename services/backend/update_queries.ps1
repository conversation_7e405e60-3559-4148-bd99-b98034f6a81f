$file = "src/db.rs"
$content = Get-Content $file -Raw

# Update all SQL queries to include the new fields
$content = $content -replace "RETURNING id, name, email, password, verified, verified_admin, created_at, updated_at, verification_token, token_expires_at, role as ""role: UserRole"", otp_secret, otp_enabled, otp_verified, email_otp, email_otp_expires_at", "RETURNING id, name, email, password, verified, verified_admin, created_at, updated_at, verification_token, token_expires_at, role as ""role: UserRole"", otp_secret, otp_enabled, otp_verified, email_otp, email_otp_expires_at, date_of_birth, why_lola"

$content = $content -replace "SELECT id, name, email, password, verified, verified_admin, created_at, updated_at, verification_token, token_expires_at, role as ""role: UserRole"", otp_secret, otp_enabled, otp_verified, email_otp, email_otp_expires_at FROM users", "SELECT id, name, email, password, verified, verified_admin, created_at, updated_at, verification_token, token_expires_at, role as ""role: UserRole"", otp_secret, otp_enabled, otp_verified, email_otp, email_otp_expires_at, date_of_birth, why_lola FROM users"

$content = $content -replace "SELECT id, name, email, password, verified, created_at, verified_admin, updated_at, verification_token, token_expires_at, role as ""role: UserRole"", otp_secret, otp_enabled, otp_verified, email_otp, email_otp_expires_at FROM users", "SELECT id, name, email, password, verified, created_at, verified_admin, updated_at, verification_token, token_expires_at, role as ""role: UserRole"", otp_secret, otp_enabled, otp_verified, email_otp, email_otp_expires_at, date_of_birth, why_lola FROM users"

# Write the updated content back to the file
Set-Content $file $content
