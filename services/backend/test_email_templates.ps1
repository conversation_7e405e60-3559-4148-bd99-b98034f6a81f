# Test Email Templates <PERSON>ript
# This script helps you preview the email templates with sample data

# Function to replace placeholders in a template
function Replace-Placeholders {
    param (
        [string]$templateContent,
        [hashtable]$placeholders
    )
    
    foreach ($key in $placeholders.Keys) {
        $templateContent = $templateContent -replace $key, $placeholders[$key]
    }
    
    return $templateContent
}

# Function to save a preview of a template
function Save-TemplatePreview {
    param (
        [string]$templatePath,
        [string]$outputPath,
        [hashtable]$placeholders
    )
    
    Write-Host "Processing template: $templatePath"
    
    if (Test-Path $templatePath) {
        $content = Get-Content -Path $templatePath -Raw
        $processedContent = Replace-Placeholders -templateContent $content -placeholders $placeholders
        Set-Content -Path $outputPath -Value $processedContent
        Write-Host "Preview saved to: $outputPath"
        return $true
    } else {
        Write-Host "Template not found: $templatePath" -ForegroundColor Red
        return $false
    }
}

# Create output directory if it doesn't exist
$previewDir = "email_previews"
if (-not (Test-Path $previewDir)) {
    New-Item -ItemType Directory -Path $previewDir | Out-Null
    Write-Host "Created preview directory: $previewDir"
}

# Sample data for templates
$otpPlaceholders = @{
    "{{username}}" = "John Doe"
    "{{otp_code}}" = "123456"
}

$welcomePlaceholders = @{
    "{{username}}" = "John Doe"
}

$verificationPlaceholders = @{
    "{{username}}" = "John Doe"
    "{{verification_link}}" = "http://localhost:8000/api/auth/verify?token=sample-token"
}

$resetPasswordPlaceholders = @{
    "{{username}}" = "John Doe"
    "{{rest_link}}" = "http://localhost:8000/reset-password?token=sample-token"
}

# Process original templates
Write-Host "Processing original templates..." -ForegroundColor Cyan
$originalOtpSuccess = Save-TemplatePreview -templatePath "src/mail/templates/OTP-email.html" -outputPath "$previewDir/original_otp.html" -placeholders $otpPlaceholders
$originalWelcomeSuccess = Save-TemplatePreview -templatePath "src/mail/templates/Welcome-email.html" -outputPath "$previewDir/original_welcome.html" -placeholders $welcomePlaceholders
$originalVerificationSuccess = Save-TemplatePreview -templatePath "src/mail/templates/Verification-email.html" -outputPath "$previewDir/original_verification.html" -placeholders $verificationPlaceholders
$originalResetSuccess = Save-TemplatePreview -templatePath "src/mail/templates/RestPassword-email.html" -outputPath "$previewDir/original_reset.html" -placeholders $resetPasswordPlaceholders

# Process enhanced templates
Write-Host "`nProcessing enhanced templates..." -ForegroundColor Cyan
$enhancedOtpSuccess = Save-TemplatePreview -templatePath "src/mail/templates/enhanced/OTP-email.html" -outputPath "$previewDir/enhanced_otp.html" -placeholders $otpPlaceholders
$enhancedWelcomeSuccess = Save-TemplatePreview -templatePath "src/mail/templates/enhanced/Welcome-email.html" -outputPath "$previewDir/enhanced_welcome.html" -placeholders $welcomePlaceholders
$enhancedVerificationSuccess = Save-TemplatePreview -templatePath "src/mail/templates/enhanced/Verification-email.html" -outputPath "$previewDir/enhanced_verification.html" -placeholders $verificationPlaceholders
$enhancedResetSuccess = Save-TemplatePreview -templatePath "src/mail/templates/enhanced/RestPassword-email.html" -outputPath "$previewDir/enhanced_reset.html" -placeholders $resetPasswordPlaceholders

# Summary
Write-Host "`nTemplate Preview Summary:" -ForegroundColor Green
Write-Host "Original Templates:"
Write-Host "  - OTP Email: $(if ($originalOtpSuccess) { 'Success' } else { 'Failed' })"
Write-Host "  - Welcome Email: $(if ($originalWelcomeSuccess) { 'Success' } else { 'Failed' })"
Write-Host "  - Verification Email: $(if ($originalVerificationSuccess) { 'Success' } else { 'Failed' })"
Write-Host "  - Reset Password Email: $(if ($originalResetSuccess) { 'Success' } else { 'Failed' })"

Write-Host "`nEnhanced Templates:"
Write-Host "  - OTP Email: $(if ($enhancedOtpSuccess) { 'Success' } else { 'Failed' })"
Write-Host "  - Welcome Email: $(if ($enhancedWelcomeSuccess) { 'Success' } else { 'Failed' })"
Write-Host "  - Verification Email: $(if ($enhancedVerificationSuccess) { 'Success' } else { 'Failed' })"
Write-Host "  - Reset Password Email: $(if ($enhancedResetSuccess) { 'Success' } else { 'Failed' })"

Write-Host "`nPreview files are saved in the '$previewDir' directory."
Write-Host "Open these HTML files in your browser to see how the emails will look."
