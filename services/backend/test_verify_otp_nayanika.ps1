$headers = @{
    "Content-Type" = "application/json"
}

# You'll need to replace this with the actual OTP code you receive
$otpCode = "REPLACE_WITH_ACTUAL_OTP"

$body = @{
    "email" = "<EMAIL>"
    "otp_code" = $otpCode
    # Include name if this is a new user
    # "name" = "Nayanika"
} | ConvertTo-Json

Write-Host "Testing /auth/verify-otp endpoint..."
$response = Invoke-RestMethod -Uri "http://localhost:8001/api/auth/verify-otp" -Method Post -Headers $headers -Body $body
$response | ConvertTo-Json

# Save the response for later use
$response | ConvertTo-Json | Out-File -FilePath "verify_otp_response_nayanika.json"
