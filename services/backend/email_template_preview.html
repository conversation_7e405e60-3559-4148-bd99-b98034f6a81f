<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Template Preview</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .template-selector {
            margin-bottom: 20px;
        }
        .preview-container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .code-container {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            overflow: auto;
        }
        textarea {
            width: 100%;
            height: 300px;
            font-family: monospace;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        h2 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background-color: #f0f0f0;
            cursor: pointer;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            margin-right: 5px;
        }
        .tab.active {
            background-color: white;
            border-bottom: 1px solid white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Email Template Preview</h1>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab('otp')">OTP Email</div>
            <div class="tab" onclick="switchTab('welcome')">Welcome Email</div>
            <div class="tab" onclick="switchTab('verification')">Verification Email</div>
            <div class="tab" onclick="switchTab('reset')">Reset Password Email</div>
        </div>
        
        <div id="otp" class="tab-content active">
            <h2>OTP Email Template</h2>
            <div class="preview-container" id="otp-preview"></div>
            <div class="code-container">
                <textarea id="otp-code"></textarea>
                <button onclick="updatePreview('otp')">Update Preview</button>
            </div>
        </div>
        
        <div id="welcome" class="tab-content">
            <h2>Welcome Email Template</h2>
            <div class="preview-container" id="welcome-preview"></div>
            <div class="code-container">
                <textarea id="welcome-code"></textarea>
                <button onclick="updatePreview('welcome')">Update Preview</button>
            </div>
        </div>
        
        <div id="verification" class="tab-content">
            <h2>Verification Email Template</h2>
            <div class="preview-container" id="verification-preview"></div>
            <div class="code-container">
                <textarea id="verification-code"></textarea>
                <button onclick="updatePreview('verification')">Update Preview</button>
            </div>
        </div>
        
        <div id="reset" class="tab-content">
            <h2>Reset Password Email Template</h2>
            <div class="preview-container" id="reset-preview"></div>
            <div class="code-container">
                <textarea id="reset-code"></textarea>
                <button onclick="updatePreview('reset')">Update Preview</button>
            </div>
        </div>
    </div>

    <script>
        // Template data
        const templates = {
            otp: {
                path: 'src/mail/templates/OTP-email.html',
                placeholders: {
                    '{{username}}': 'John Doe',
                    '{{otp_code}}': '123456'
                }
            },
            welcome: {
                path: 'src/mail/templates/Welcome-email.html',
                placeholders: {
                    '{{username}}': 'John Doe'
                }
            },
            verification: {
                path: 'src/mail/templates/Verification-email.html',
                placeholders: {
                    '{{username}}': 'John Doe',
                    '{{verification_link}}': 'http://localhost:8000/api/auth/verify?token=sample-token'
                }
            },
            reset: {
                path: 'src/mail/templates/RestPassword-email.html',
                placeholders: {
                    '{{username}}': 'John Doe',
                    '{{rest_link}}': 'http://localhost:8000/reset-password?token=sample-token'
                }
            }
        };

        // Initial template loading
        window.onload = function() {
            loadTemplate('otp');
            loadTemplate('welcome');
            loadTemplate('verification');
            loadTemplate('reset');
        };

        // Load template from file
        async function loadTemplate(type) {
            try {
                const response = await fetch(templates[type].path);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const text = await response.text();
                document.getElementById(`${type}-code`).value = text;
                updatePreview(type);
            } catch (error) {
                console.error('Error loading template:', error);
                // If we can't load the template, use the default one
                const defaultTemplate = getDefaultTemplate(type);
                document.getElementById(`${type}-code`).value = defaultTemplate;
                updatePreview(type);
            }
        }

        // Update preview with current template code
        function updatePreview(type) {
            const code = document.getElementById(`${type}-code`).value;
            let previewHtml = code;
            
            // Replace placeholders
            for (const [placeholder, value] of Object.entries(templates[type].placeholders)) {
                previewHtml = previewHtml.replace(new RegExp(placeholder, 'g'), value);
            }
            
            document.getElementById(`${type}-preview`).innerHTML = previewHtml;
        }

        // Switch between tabs
        function switchTab(type) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(type).classList.add('active');
            document.querySelector(`.tab[onclick="switchTab('${type}')"]`).classList.add('active');
        }

        // Get default template if loading fails
        function getDefaultTemplate(type) {
            switch(type) {
                case 'otp':
                    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your OTP Code</title>
</head>
<body style="font-family: Arial, sans-serif; background-color: #f4f4f4; padding: 20px;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px;">
        <h2 style="color: #333333;">Your One-Time Password</h2>
        <p style="color: #555555;">Hello, {{username}}!</p>
        <p style="color: #555555;">Here is your one-time password (OTP) for authentication:</p>
        <div style="background-color: #f0f0f0; padding: 15px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0; border-radius: 5px;">
            {{otp_code}}
        </div>
        <p style="color: #555555;">This code will expire in 10 minutes.</p>
        <p style="color: #555555;">If you did not request this code, please ignore this email.</p>
        <p style="color: #555555;">Best regards,</p>
        <p style="color: #555555;">The Lola Team</p>
    </div>
</body>
</html>`;
                case 'welcome':
                    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome Email</title>
</head>
<body style="font-family: Arial, sans-serif; background-color: #f4f4f4; padding: 20px;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px;">
        <h2 style="color: #333333;">Welcome to Our Application!</h2>
        <p style="color: #555555;">Hello, {{username}}!</p>
        <p style="color: #555555;">Thank you for registering at our application. We're excited to have you on board.</p>
        <p style="color: #555555;">If you have any questions, feel free to reply to this email or visit our support page.</p>
        <p style="color: #555555;">Best regards,</p>
        <p style="color: #555555;">The Application Team</p>
    </div>
</body>
</html>`;
                case 'verification':
                    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification</title>
</head>
<body style="font-family: Arial, sans-serif; background-color: #f4f4f4; padding: 20px;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px;">
        <h2 style="color: #333333;">Email Verification</h2>
        <p style="color: #555555;">Hello, {{username}}!</p>
        <p style="color: #555555;">Thank you for registering at our application. Please click the link below to verify your email address:</p>
        <a href="{{verification_link}}" style="display: inline-block; padding: 10px 20px; font-size: 16px; color: #ffffff; background-color: #007bff; text-decoration: none; border-radius: 5px;">Verify Email</a>
        <p style="color: #555555;">If you did not register, please ignore this email.</p>
        <p style="color: #555555;">Best regards,</p>
        <p style="color: #555555;">The Application Team</p>
    </div>
</body>
</html>`;
                case 'reset':
                    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password</title>
</head>
<body style="font-family: Arial, sans-serif; background-color: #f4f4f4; padding: 20px;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px;">
        <h2 style="color: #333333;">Reset Your Password</h2>
        <p style="color: #555555;">Hello, {{username}}!</p>
        <p style="color: #555555;">We received a request to reset your password. Please click the link below to set a new password:</p>
        <a href="{{rest_link}}" style="display: inline-block; padding: 10px 20px; font-size: 16px; color: #ffffff; background-color: #007bff; text-decoration: none; border-radius: 5px;">Reset Password</a>
        <p style="color: #555555;">If you did not request a password reset, please ignore this email.</p>
        <p style="color: #555555;">This link will expire in 30 minutes.</p>
        <p style="color: #555555;">Best regards,</p>
        <p style="color: #555555;">The Application Team</p>
    </div>
</body>
</html>`;
                default:
                    return '';
            }
        }
    </script>
</body>
</html>
