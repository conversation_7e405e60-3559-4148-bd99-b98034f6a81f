# Builder stage: Build your application
FROM rust:1.82.0-slim as builder

# Accept DATABASE_URL and SQLX_OFFLINE as build arguments
ARG DATABASE_URL
ARG SQLX_OFFLINE

WORKDIR /usr/src/app
COPY . .

# Install required dependencies
RUN apt-get update && \
    apt-get install -y pkg-config libssl-dev && \
    rm -rf /var/lib/apt/lists/*

# Build the application in release mode
ENV DATABASE_URL=${DATABASE_URL}
ENV SQLX_OFFLINE=${SQLX_OFFLINE}
RUN cargo build --release

# SQLx CLI stage: Install the SQLx CLI (for running migrations)
FROM rust:1.82.0-slim as sqlx-cli
RUN cargo install sqlx-cli --version 0.7.1 --no-default-features --features postgres

# Final runtime stage
FROM debian:bookworm-slim

WORKDIR /app

# Install runtime dependencies
RUN apt-get update && \
    apt-get install -y libssl3 ca-certificates netcat-openbsd && \
    rm -rf /var/lib/apt/lists/*

# Copy the built binary from the builder stage
COPY --from=builder /usr/src/app/target/release/lola-backend /app/
# Copy the mail templates
COPY --from=builder /usr/src/app/src/mail/templates /app/src/mail/templates
# Copy the migrations folder (ensure your migrations are in the context)
COPY --from=builder /usr/src/app/migrations /app/migrations

# Copy SQLx CLI from its stage
COPY --from=sqlx-cli /usr/local/cargo/bin/sqlx /usr/local/bin/sqlx

# Set the command to run migrations then start your server
CMD ["sh", "-c", "echo 'Running database migrations...' && echo 'Starting lola-backend...' && ./lola-backend"]
