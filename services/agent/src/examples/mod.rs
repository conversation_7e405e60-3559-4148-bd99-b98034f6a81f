//! # Examples
// How this is being organized:
// Due to limitation what documentation is being generated by rust docs, I had to cheat a little
// Files included in this module are not real examples, but only a documentation entry
// Thanks to include_str! macro I was able to include the whole content of example files
// and add some additional notes
//! Each example can be easily run by using command:
//! ```bash
//! cargo run --example <example_name>
//! ```
//!
//! List of all available examples can be found below

pub mod simple;
pub mod struct_output;
pub mod tools_search;
pub mod tools_custom;
pub mod tools_mcp;
