# UI Library (ApatiteUI)

## Overview

ApatiteUI is a shared UI component library designed to provide a consistent design system across both React Native and Next.js applications in the Saday Agent platform. It leverages Tailwind CSS for styling, with NativeWind providing the bridge for React Native compatibility.

## Features

- **Cross-platform Components**: All components work seamlessly in both React Native and Next.js
- **Tailwind CSS Styling**: Uses Tailwind CSS for consistent styling across platforms
- **NativeWind Integration**: Enables Tailwind CSS usage in React Native
- **TypeScript Support**: Fully typed components for better developer experience
- **Consistent Design Language**: Maintains visual consistency between web and mobile applications

## Architecture

The UI library is structured as a standalone package within the monorepo:

```
saday-agent/
├── packages/
│   └── apatiteui/        # Shared UI component library
│       ├── src/
│       │   ├── components/   # UI components
│       │   ├── utils/        # Utility functions
│       │   └── index.tsx     # Main export file
│       ├── dist/             # Built files (generated)
│       ├── tailwind.config.js # Tailwind configuration
│       └── package.json      # Package configuration
```

## Installation

The UI library is installed as a workspace dependency in both the web and mobile applications:

```bash
# From the root of the monorepo
pnpm install
pnpm build:ui
```

## Usage

### In React Native (with NativeWind)

```tsx
import React from 'react';
import { View } from 'react-native';
import { Button, Text, Card, CardContent } from 'apatiteui';

export default function MyComponent() {
  return (
    <View className="p-4">
      <Text variant="h1">Hello from ApatiteUI</Text>
      <Card className="mt-4">
        <CardContent>
          <Text>This is a card component</Text>
        </CardContent>
      </Card>
      <Button 
        title="Click Me" 
        variant="primary" 
        onPress={() => console.log('Button pressed')} 
        className="mt-4"
      />
    </View>
  );
}
```

### In Next.js (with Tailwind CSS)

```tsx
import React from 'react';
import { Button, Text, Card, CardContent } from 'apatiteui';

export default function MyPage() {
  return (
    <div className="p-4">
      <Text variant="h1">Hello from ApatiteUI</Text>
      <Card className="mt-4">
        <CardContent>
          <Text>This is a card component</Text>
        </CardContent>
      </Card>
      <Button 
        title="Click Me" 
        variant="primary" 
        onPress={() => console.log('Button pressed')} 
        className="mt-4"
      />
    </div>
  );
}
```

## Components

### Button

A customizable button component with various variants and states.

```tsx
<Button 
  title="Click Me" 
  variant="primary" // 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'
  size="md" // 'sm' | 'md' | 'lg'
  fullWidth={false}
  disabled={false}
  loading={false}
  onPress={() => {}}
  className="custom-class"
  textClassName="custom-text-class"
/>
```

### Text

A text component with various typography variants and weights.

```tsx
<Text 
  variant="body" // 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body' | 'caption' | 'label'
  weight="normal" // 'light' | 'normal' | 'medium' | 'semibold' | 'bold'
  className="custom-class"
>
  Hello World
</Text>
```

### Card

A card component with header, content, and footer sections.

```tsx
<Card className="custom-class">
  <CardHeader>
    <CardTitle>
      <Text variant="h4">Card Title</Text>
    </CardTitle>
    <CardDescription>
      <Text variant="caption">Card Description</Text>
    </CardDescription>
  </CardHeader>
  <CardContent>
    <Text>Card content goes here</Text>
  </CardContent>
  <CardFooter>
    <Button title="Action" size="sm" onPress={() => {}} />
  </CardFooter>
</Card>
```

## Development

### Building the Library

```bash
# From the root of the monorepo
pnpm build:ui
```

### Watching for Changes

```bash
# From the root of the monorepo
pnpm dev:ui
```

## Integration with Applications

### React Native Integration

The UI library is integrated with the React Native application through:

1. Workspace dependency in `package.json`:
   ```json
   "dependencies": {
     "apatiteui": "workspace:*",
     // other dependencies
   }
   ```

2. NativeWind configuration in the React Native project for Tailwind CSS support

### Next.js Integration

The UI library is integrated with the Next.js application through:

1. Workspace dependency in `package.json`:
   ```json
   "dependencies": {
     "apatiteui": "workspace:*",
     // other dependencies
   }
   ```

2. Tailwind CSS configuration in `tailwind.config.ts` to include the UI library components:
   ```ts
   content: [
     // other content paths
     "../packages/apatiteui/src/**/*.{js,ts,jsx,tsx}",
   ]
   ```

## Best Practices

1. **Component Design**: Design components to be platform-agnostic, using platform detection utilities when necessary
2. **Styling**: Use Tailwind CSS classes for styling to maintain consistency
3. **TypeScript**: Ensure all components are properly typed
4. **Documentation**: Document all components with examples for both platforms
5. **Testing**: Test components on both platforms before releasing
