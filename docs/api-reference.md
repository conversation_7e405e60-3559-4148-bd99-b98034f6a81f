# API Reference

This document provides a comprehensive reference for the Saday Agent API endpoints.

## Base URL

All API endpoints are prefixed with: `http://your-domain.com/api`

For local development: `http://localhost:8001/api`

## Authentication

Most endpoints require authentication via JW<PERSON> token. The token can be provided in two ways:

1. As a cookie (set automatically after login)
2. In the Authorization header: `Authorization: Bearer YOUR_JWT_TOKEN`

## Response Format

All API responses follow a consistent format:

### Success Response

```json
{
  "status": "success",
  "data": { ... } // or message: "Success message"
}
```

### Error Response

```json
{
  "status": "error",
  "message": "Error description"
}
```

## Authentication Endpoints

### Request OTP

Sends an OTP code to the specified email for login or registration.

- **URL**: `/auth/request-otp`
- **Method**: `POST`
- **Auth required**: No
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>"
  }
  ```
- **Success Response**:
  - **Code**: 200 OK
  - **Content**:
    ```json
    {
      "status": "success",
      "userType": "New", // or "Existing"
      "message": "OTP sent to email"
    }
    ```

### Verify OTP

Verifies the OTP and authenticates the user.

- **URL**: `/auth/verify-otp`
- **Method**: `POST`
- **Auth required**: No
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "otp_code": "123456",
    "name": "John Doe", // Required only for new users
    "profile_data": { // Optional
      "date_of_birth": "1990-01-01",
      "why_lola": "meditation", // one of: "meditation", "journaling", "both"
      "interests": ["mindfulness", "yoga"],
      "preferences": {
        "theme": "dark",
        "notifications": true
      },
      "bio": "Mindfulness enthusiast",
      "location": "San Francisco, CA"
    }
  }
  ```
- **Success Response**:
  - **Code**: 200 OK (existing user) or 201 Created (new user)
  - **Content**:
    ```json
    {
      "status": "success",
      "token": "jwt_token_here",
      "userType": "New" // or "Existing"
    }
    ```

### Resend OTP

Resends the OTP to the specified email address.

- **URL**: `/auth/resend-otp`
- **Method**: `POST`
- **Auth required**: No
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>"
  }
  ```
- **Success Response**:
  - **Code**: 200 OK
  - **Content**:
    ```json
    {
      "status": "success",
      "message": "OTP resent to email"
    }
    ```

## User Endpoints

### Get Current User

Returns the currently authenticated user's information.

- **URL**: `/users/me`
- **Method**: `GET`
- **Auth required**: Yes
- **Success Response**:
  - **Code**: 200 OK
  - **Content**:
    ```json
    {
      "status": "success",
      "data": {
        "user": {
          "id": "user_id",
          "name": "John Doe",
          "email": "<EMAIL>",
          "role": "user",
          "profile_data": {
            "date_of_birth": "1990-01-01",
            "why_lola": "meditation",
            "interests": ["mindfulness", "yoga"],
            "preferences": {
              "theme": "dark",
              "notifications": true
            },
            "bio": "Mindfulness enthusiast",
            "location": "San Francisco, CA"
          },
          "createdAt": "2023-01-01T00:00:00Z",
          "updatedAt": "2023-01-01T00:00:00Z"
        }
      }
    }
    ```

### Update User

Updates the current user's information.

- **URL**: `/users/me`
- **Method**: `PATCH`
- **Auth required**: Yes
- **Request Body**:
  ```json
  {
    "name": "New Name",
    "date_of_birth": "1990-01-01",
    "why_lola": "journaling"
  }
  ```
- **Success Response**:
  - **Code**: 200 OK
  - **Content**:
    ```json
    {
      "status": "success",
      "data": {
        "id": "user_id",
        "name": "New Name",
        "email": "<EMAIL>",
        "role": "user",
        "date_of_birth": "1990-01-01",
        "why_lola": "journaling",
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-01-01T00:00:00Z"
      }
    }
    ```

## Agent Endpoints

### Create Agent

Creates a new agent.

- **URL**: `/agents`
- **Method**: `POST`
- **Auth required**: Yes
- **Request Body**:
  ```json
  {
    "name": "My Agent",
    "description": "Agent description",
    "configuration": {
      "model": "gpt-4",
      "temperature": 0.7,
      "tools": ["web-search", "calculator"]
    }
  }
  ```
- **Success Response**:
  - **Code**: 201 Created
  - **Content**:
    ```json
    {
      "status": "success",
      "data": {
        "id": "agent_id",
        "name": "My Agent",
        "description": "Agent description",
        "configuration": {
          "model": "gpt-4",
          "temperature": 0.7,
          "tools": ["web-search", "calculator"]
        },
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-01-01T00:00:00Z"
      }
    }
    ```

### Get Agents

Returns a list of agents owned by the current user.

- **URL**: `/agents`
- **Method**: `GET`
- **Auth required**: Yes
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Number of items per page (default: 10)
- **Success Response**:
  - **Code**: 200 OK
  - **Content**:
    ```json
    {
      "status": "success",
      "data": {
        "agents": [
          {
            "id": "agent_id",
            "name": "My Agent",
            "description": "Agent description",
            "createdAt": "2023-01-01T00:00:00Z",
            "updatedAt": "2023-01-01T00:00:00Z"
          }
        ],
        "total": 1,
        "page": 1,
        "limit": 10,
        "totalPages": 1
      }
    }
    ```

### Get Agent

Returns a specific agent by ID.

- **URL**: `/agents/:id`
- **Method**: `GET`
- **Auth required**: Yes
- **Success Response**:
  - **Code**: 200 OK
  - **Content**:
    ```json
    {
      "status": "success",
      "data": {
        "id": "agent_id",
        "name": "My Agent",
        "description": "Agent description",
        "configuration": {
          "model": "gpt-4",
          "temperature": 0.7,
          "tools": ["web-search", "calculator"]
        },
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-01-01T00:00:00Z"
      }
    }
    ```

## Chat Endpoints

### Create Chat

Creates a new chat session with an agent.

- **URL**: `/chats`
- **Method**: `POST`
- **Auth required**: Yes
- **Request Body**:
  ```json
  {
    "agentId": "agent_id",
    "title": "Chat Title"
  }
  ```
- **Success Response**:
  - **Code**: 201 Created
  - **Content**:
    ```json
    {
      "status": "success",
      "data": {
        "id": "chat_id",
        "agentId": "agent_id",
        "title": "Chat Title",
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-01-01T00:00:00Z"
      }
    }
    ```

### Send Message

Sends a message to a chat session.

- **URL**: `/chats/:chatId/messages`
- **Method**: `POST`
- **Auth required**: Yes
- **Request Body**:
  ```json
  {
    "content": "Hello, agent!"
  }
  ```
- **Success Response**:
  - **Code**: 201 Created
  - **Content**:
    ```json
    {
      "status": "success",
      "data": {
        "id": "message_id",
        "chatId": "chat_id",
        "role": "user",
        "content": "Hello, agent!",
        "createdAt": "2023-01-01T00:00:00Z"
      }
    }
    ```

## WebSocket API

The WebSocket API enables real-time communication for chat messages and agent actions.

### Connection

Connect to the WebSocket server:

```
ws://your-domain.com/ws?token=YOUR_JWT_TOKEN
```

### Message Format

Messages sent and received through the WebSocket connection follow this format:

```json
{
  "type": "message_type",
  "payload": {
    // Message-specific data
  }
}
```

### Message Types

#### Chat Message

```json
{
  "type": "chat_message",
  "payload": {
    "id": "message_id",
    "chatId": "chat_id",
    "role": "assistant",
    "content": "Hello, I'm your agent!",
    "createdAt": "2023-01-01T00:00:00Z"
  }
}
```

#### Agent Action

```json
{
  "type": "agent_action",
  "payload": {
    "id": "action_id",
    "chatId": "chat_id",
    "agentId": "agent_id",
    "action": "web_search",
    "parameters": {
      "query": "search query"
    },
    "status": "in_progress",
    "createdAt": "2023-01-01T00:00:00Z"
  }
}
```

## Error Codes

| Status Code | Description                                          |
|-------------|------------------------------------------------------|
| 400         | Bad Request - Invalid input data                     |
| 401         | Unauthorized - Authentication required               |
| 403         | Forbidden - Insufficient permissions                 |
| 404         | Not Found - Resource not found                       |
| 409         | Conflict - Resource already exists                   |
| 422         | Unprocessable Entity - Validation error              |
| 429         | Too Many Requests - Rate limit exceeded              |
| 500         | Internal Server Error - Server-side error            |
