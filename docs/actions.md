# Agent Orchestration Layer Documentation

## Overview

The Agent Orchestration Layer is the central component that manages the lifecycle, execution, and integration of AI agents within the Saday Agent platform. This document provides comprehensive documentation on the architecture, components, interfaces, and best practices for building and extending the orchestration layer.

## Architecture

The orchestration layer follows a modular, service-oriented architecture with the following core components:

1. **Agent Lifecycle Management** - Handles agent creation, configuration, and state transitions
2. **Tool Registration and Discovery** - Manages the registration and discovery of tools that agents can use
3. **Execution Flow Control** - Controls the execution flow of agents, including input/output handling and tool invocations
4. **State Management and Persistence** - Manages agent state and conversation history
5. **Authentication and Authorization** - Integrates with the main authentication system and enforces access control
6. **Observability and Monitoring** - Provides telemetry, logging, and monitoring capabilities
7. **Error Handling and Recovery** - Implements robust error handling and recovery mechanisms

## Components

### 1. Agent Lifecycle Management

The Agent Lifecycle Management component handles the complete lifecycle of agents, from creation to archival.

#### Agent States

```rust
pub enum AgentState {
    Created,    // Agent has been created but not fully configured
    Configured, // Agent is fully configured and ready to run
    Running,    // Agent is currently executing
    Paused,     // Agent execution is paused
    Completed,  // Agent has completed its execution
    Failed,     // Agent execution has failed
    Archived,   // Agent has been archived
}
```

#### Agent Configuration

```rust
pub struct AgentConfig {
    pub id: Uuid,                    // Unique identifier
    pub name: String,                // Display name
    pub description: String,         // Description
    pub model: String,               // AI model to use
    pub tools: Vec<ToolConfig>,      // Tools available to the agent
    pub system_prompt: String,       // System prompt for the agent
    pub max_iterations: usize,       // Maximum number of iterations
    pub team_id: Option<Uuid>,       // Team that owns the agent
    pub created_by: Uuid,            // User who created the agent
    pub created_at: DateTime<Utc>,   // Creation timestamp
    pub updated_at: DateTime<Utc>,   // Last update timestamp
}
```

#### Agent Manager Service

The Agent Manager Service provides methods for creating, updating, retrieving, and deleting agents:

- `create_agent(config: AgentConfig) -> Result<Uuid, AgentError>`
- `update_agent(id: Uuid, config: AgentConfig) -> Result<(), AgentError>`
- `get_agent(id: Uuid) -> Result<AgentConfig, AgentError>`
- `list_agents(filters: AgentFilters) -> Result<Vec<AgentConfig>, AgentError>`
- `delete_agent(id: Uuid) -> Result<(), AgentError>`

### 2. Tool Registration and Discovery

The Tool Registration and Discovery component manages the tools that agents can use.

#### Tool Configuration

```rust
pub struct ToolConfig {
    pub id: Uuid,                    // Unique identifier
    pub name: String,                // Tool name
    pub description: String,         // Tool description
    pub schema: serde_json::Value,   // JSON Schema for tool parameters
    pub handler_type: ToolHandlerType, // Type of handler
    pub handler_config: serde_json::Value, // Handler configuration
    pub created_at: DateTime<Utc>,   // Creation timestamp
    pub updated_at: DateTime<Utc>,   // Last update timestamp
}

pub enum ToolHandlerType {
    Http,      // HTTP API call
    Function,  // In-process function call
    Database,  // Database query
    Custom,    // Custom handler
}
```

#### Tool Registry Service

The Tool Registry Service provides methods for registering, retrieving, and managing tools:

- `register_tool(config: ToolConfig) -> Result<Uuid, ToolError>`
- `get_tool(id: Uuid) -> Result<ToolConfig, ToolError>`
- `list_tools(filters: ToolFilters) -> Result<Vec<ToolConfig>, ToolError>`
- `update_tool(id: Uuid, config: ToolConfig) -> Result<(), ToolError>`
- `delete_tool(id: Uuid) -> Result<(), ToolError>`

### 3. Execution Flow Control

The Execution Flow Control component manages the execution of agents, handling input/output, state transitions, and tool invocations.

#### Execution Context

```rust
pub struct ExecutionContext {
    pub agent_id: Uuid,              // Agent ID
    pub execution_id: Uuid,          // Execution ID
    pub user_id: Uuid,               // User ID
    pub input: String,               // User input
    pub state: HashMap<String, serde_json::Value>, // Execution state
    pub history: Vec<Message>,       // Conversation history
    pub created_at: DateTime<Utc>,   // Creation timestamp
    pub updated_at: DateTime<Utc>,   // Last update timestamp
}

pub struct Message {
    pub role: MessageRole,           // Message role
    pub content: String,             // Message content
    pub tool_calls: Option<Vec<ToolCall>>, // Tool calls
    pub timestamp: DateTime<Utc>,    // Timestamp
}

pub enum MessageRole {
    User,      // User message
    Assistant, // Assistant message
    System,    // System message
    Tool,      // Tool message
}

pub struct ToolCall {
    pub tool_id: Uuid,               // Tool ID
    pub tool_name: String,           // Tool name
    pub arguments: serde_json::Value, // Tool arguments
    pub result: Option<serde_json::Value>, // Tool result
    pub error: Option<String>,       // Error message
    pub start_time: DateTime<Utc>,   // Start timestamp
    pub end_time: Option<DateTime<Utc>>, // End timestamp
}
```

#### Execution Service

The Execution Service provides methods for starting, monitoring, and controlling agent executions:

- `start_execution(agent_id: Uuid, user_id: Uuid, input: String) -> Result<Uuid, ExecutionError>`
- `get_execution_status(execution_id: Uuid) -> Result<ExecutionStatus, ExecutionError>`
- `handle_tool_call(execution_id: Uuid, tool_call: ToolCall) -> Result<serde_json::Value, ExecutionError>`
- `complete_execution(execution_id: Uuid, final_output: String) -> Result<(), ExecutionError>`
- `abort_execution(execution_id: Uuid) -> Result<(), ExecutionError>`

### 4. State Management and Persistence

The State Management and Persistence component handles the storage and retrieval of agent state and conversation history.

#### State Manager

The State Manager provides methods for saving and loading execution state:

- `save_state(execution_id: Uuid, state: HashMap<String, serde_json::Value>) -> Result<(), StateError>`
- `load_state(execution_id: Uuid) -> Result<HashMap<String, serde_json::Value>, StateError>`
- `add_message(execution_id: Uuid, message: Message) -> Result<(), StateError>`
- `get_history(execution_id: Uuid) -> Result<Vec<Message>, StateError>`

### 5. Authentication and Authorization

The Authentication and Authorization component integrates with the main authentication system and enforces access control.

#### Auth Service

The Auth Service provides methods for validating tokens and checking permissions:

- `validate_token(token: &str) -> Result<Claims, AuthError>`
- `check_agent_access(user_id: Uuid, agent_id: Uuid, required_permission: Permission) -> Result<bool, AuthError>`
- `check_tool_access(user_id: Uuid, tool_id: Uuid, required_permission: Permission) -> Result<bool, AuthError>`

### 6. Observability and Monitoring

The Observability and Monitoring component provides telemetry, logging, and monitoring capabilities.

#### Telemetry Service

The Telemetry Service provides methods for recording metrics and traces:

- `record_execution_metrics(execution_id: Uuid, metrics: ExecutionMetrics) -> Result<(), TelemetryError>`
- `start_execution_span(execution_id: Uuid, agent_id: Uuid, user_id: Uuid) -> Result<SpanContext, TelemetryError>`
- `end_execution_span(span_context: SpanContext, status: SpanStatus) -> Result<(), TelemetryError>`
- `record_tool_call_span(parent_span: SpanContext, tool_call: ToolCall) -> Result<(), TelemetryError>`

### 7. Error Handling and Recovery

The Error Handling and Recovery component implements robust error handling and recovery mechanisms.

#### Recovery Service

The Recovery Service provides methods for recovering failed executions:

- `recover_execution(execution_id: Uuid) -> Result<(), RecoveryError>`
- `check_stalled_executions() -> Result<Vec<Uuid>, RecoveryError>`
- `handle_stalled_executions() -> Result<(), RecoveryError>`

## Best Practices

### Agent Design

1. **Single Responsibility**: Each agent should have a clear, focused purpose.
2. **Composability**: Design agents to be composable, allowing complex tasks to be broken down.
3. **Idempotency**: Agent actions should be idempotent where possible.
4. **Graceful Degradation**: Agents should gracefully handle failures and degraded conditions.
5. **Prompt Engineering**: Use well-crafted prompts that clearly define the agent's role and constraints.

### Tool Integration

1. **Schema Validation**: Always validate tool inputs against a JSON Schema.
2. **Error Handling**: Tools should provide clear error messages and handle exceptions gracefully.
3. **Rate Limiting**: Implement rate limiting for external API calls.
4. **Caching**: Cache tool results where appropriate to improve performance.
5. **Versioning**: Version tools to allow for backward compatibility.

### Security

1. **Principle of Least Privilege**: Agents and tools should have the minimum permissions needed.
2. **Input Validation**: Validate all inputs to prevent injection attacks.
3. **Secure Credentials**: Never expose credentials in prompts or responses.
4. **Audit Logging**: Log all agent actions for audit purposes.
5. **Regular Security Reviews**: Conduct regular security reviews of agent configurations.

### Testing

1. **Unit Testing**: Test individual components in isolation.
2. **Integration Testing**: Test the interaction between components.
3. **End-to-End Testing**: Test complete agent workflows.
4. **Regression Testing**: Ensure new changes don't break existing functionality.
5. **Performance Testing**: Test under load to ensure performance requirements are met.

## Multi-Agent Orchestration

The orchestration layer supports multi-agent systems where multiple specialized AI agents collaborate to achieve complex objectives. This section outlines the architecture, design patterns, and best practices for implementing multi-agent systems.

### Multi-Agent Architecture

Multi-agent systems break down problems into distinct tasks handled by agents with defined roles who interact dynamically. Each agent can be an independent entity with its own:

- **Role**: Specific responsibility or expertise
- **Context**: Knowledge and state relevant to its role
- **Model**: Potentially different LLM for each agent
- **Tools**: Specialized tools relevant to its domain

#### Advantages of Multi-Agent Systems

- **Enhanced Accuracy**: Agents can cross-check each other's work
- **Improved Efficiency**: Agents can work in parallel
- **Better Handling of Complex Tasks**: Breaking down large tasks into manageable subtasks
- **Increased Scalability**: Add more specialized agents as needed
- **Improved Fault Tolerance**: If one agent fails, others can take over
- **Reduced Hallucinations and Bias**: Combining perspectives from multiple agents

### Multi-Agent Design Patterns

The orchestration layer supports several design patterns for multi-agent systems:

#### 1. Sequential Pattern

Agents work in a linear fashion, with each agent completing its task before passing the output to the next agent, similar to an assembly line.

```rust
pub struct SequentialOrchestrator {
    agents: Vec<AgentConfig>,
    execution_service: Arc<ExecutionService>,
}

impl SequentialOrchestrator {
    pub async fn execute(&self, input: String, user_id: Uuid) -> Result<String, OrchestratorError> {
        let mut current_input = input;

        for agent_config in &self.agents {
            // Execute agent with current input
            let execution_id = self.execution_service.start_execution(
                agent_config.id,
                user_id,
                current_input.clone(),
            ).await?;

            // Wait for execution to complete
            self.execution_service.wait_for_completion(execution_id).await?;

            // Get output
            let execution = self.execution_service.get_execution(execution_id).await?;
            current_input = execution.output.unwrap_or_default();
        }

        Ok(current_input)
    }
}
```

#### 2. Hierarchical Pattern

Agents are organized in a hierarchy with a "manager" or Orchestrator Agent coordinating the workflow and delegating tasks to "worker" agents.

```rust
pub struct HierarchicalOrchestrator {
    manager_agent: AgentConfig,
    worker_agents: HashMap<String, AgentConfig>,
    execution_service: Arc<ExecutionService>,
}

impl HierarchicalOrchestrator {
    pub async fn execute(&self, input: String, user_id: Uuid) -> Result<String, OrchestratorError> {
        // Execute manager agent to determine which worker to use
        let manager_execution_id = self.execution_service.start_execution(
            self.manager_agent.id,
            user_id,
            input.clone(),
        ).await?;

        // Wait for manager to complete
        self.execution_service.wait_for_completion(manager_execution_id).await?;

        // Get manager output (should specify which worker to use)
        let manager_execution = self.execution_service.get_execution(manager_execution_id).await?;
        let worker_name = manager_execution.output.unwrap_or_default();

        // Find the specified worker agent
        let worker_agent = self.worker_agents.get(&worker_name)
            .ok_or_else(|| OrchestratorError::Internal(format!("Worker agent not found: {}", worker_name)))?;

        // Execute worker agent
        let worker_execution_id = self.execution_service.start_execution(
            worker_agent.id,
            user_id,
            input,
        ).await?;

        // Wait for worker to complete
        self.execution_service.wait_for_completion(worker_execution_id).await?;

        // Get worker output
        let worker_execution = self.execution_service.get_execution(worker_execution_id).await?;
        let output = worker_execution.output.unwrap_or_default();

        Ok(output)
    }
}
```

#### 3. Collaborative Pattern

Agents work together, sharing information and resources to achieve a common goal. A Response Mixer Agent often combines elements from different agent responses.

```rust
pub struct CollaborativeOrchestrator {
    specialist_agents: Vec<AgentConfig>,
    mixer_agent: AgentConfig,
    execution_service: Arc<ExecutionService>,
}

impl CollaborativeOrchestrator {
    pub async fn execute(&self, input: String, user_id: Uuid) -> Result<String, OrchestratorError> {
        // Execute all specialist agents in parallel
        let mut execution_futures = Vec::new();

        for agent_config in &self.specialist_agents {
            let execution_service = self.execution_service.clone();
            let agent_id = agent_config.id;
            let input_clone = input.clone();
            let user_id_clone = user_id;

            let future = tokio::spawn(async move {
                let execution_id = execution_service.start_execution(
                    agent_id,
                    user_id_clone,
                    input_clone,
                ).await?;

                execution_service.wait_for_completion(execution_id).await?;

                let execution = execution_service.get_execution(execution_id).await?;
                let output = execution.output.unwrap_or_default();

                Ok::<_, OrchestratorError>((agent_id, output))
            });

            execution_futures.push(future);
        }

        // Wait for all specialist agents to complete
        let results = futures::future::join_all(execution_futures).await;

        // Collect outputs from all specialist agents
        let mut specialist_outputs = HashMap::new();
        for result in results {
            let (agent_id, output) = result??;
            specialist_outputs.insert(agent_id.to_string(), output);
        }

        // Create input for mixer agent
        let mixer_input = serde_json::to_string(&specialist_outputs)?;

        // Execute mixer agent
        let mixer_execution_id = self.execution_service.start_execution(
            self.mixer_agent.id,
            user_id,
            mixer_input,
        ).await?;

        // Wait for mixer to complete
        self.execution_service.wait_for_completion(mixer_execution_id).await?;

        // Get mixer output
        let mixer_execution = self.execution_service.get_execution(mixer_execution_id).await?;
        let output = mixer_execution.output.unwrap_or_default();

        Ok(output)
    }
}
```

#### 4. Competitive Pattern

Agents compete to achieve the best outcome, with a selection mechanism to choose the best result.

```rust
pub struct CompetitiveOrchestrator {
    competing_agents: Vec<AgentConfig>,
    judge_agent: AgentConfig,
    execution_service: Arc<ExecutionService>,
}

impl CompetitiveOrchestrator {
    pub async fn execute(&self, input: String, user_id: Uuid) -> Result<String, OrchestratorError> {
        // Execute all competing agents in parallel
        let mut execution_futures = Vec::new();

        for agent_config in &self.competing_agents {
            let execution_service = self.execution_service.clone();
            let agent_id = agent_config.id;
            let input_clone = input.clone();
            let user_id_clone = user_id;

            let future = tokio::spawn(async move {
                let execution_id = execution_service.start_execution(
                    agent_id,
                    user_id_clone,
                    input_clone,
                ).await?;

                execution_service.wait_for_completion(execution_id).await?;

                let execution = execution_service.get_execution(execution_id).await?;
                let output = execution.output.unwrap_or_default();

                Ok::<_, OrchestratorError>((agent_id, output))
            });

            execution_futures.push(future);
        }

        // Wait for all competing agents to complete
        let results = futures::future::join_all(execution_futures).await;

        // Collect outputs from all competing agents
        let mut competing_outputs = HashMap::new();
        for result in results {
            let (agent_id, output) = result??;
            competing_outputs.insert(agent_id.to_string(), output);
        }

        // Create input for judge agent
        let judge_input = serde_json::to_string(&competing_outputs)?;

        // Execute judge agent
        let judge_execution_id = self.execution_service.start_execution(
            self.judge_agent.id,
            user_id,
            judge_input,
        ).await?;

        // Wait for judge to complete
        self.execution_service.wait_for_completion(judge_execution_id).await?;

        // Get judge output (should be the ID of the winning agent)
        let judge_execution = self.execution_service.get_execution(judge_execution_id).await?;
        let winning_agent_id = judge_execution.output.unwrap_or_default();

        // Return the output of the winning agent
        Ok(competing_outputs.get(&winning_agent_id).cloned().unwrap_or_default())
    }
}
```

#### 5. Diamond Pattern

A variation of the hierarchical pattern where responses from specialized agents pass through a central moderation agent before reaching the user.

```rust
pub struct DiamondOrchestrator {
    router_agent: AgentConfig,
    specialist_agents: HashMap<String, AgentConfig>,
    rephraser_agent: AgentConfig,
    execution_service: Arc<ExecutionService>,
}

impl DiamondOrchestrator {
    pub async fn execute(&self, input: String, user_id: Uuid) -> Result<String, OrchestratorError> {
        // Execute router agent to determine which specialist to use
        let router_execution_id = self.execution_service.start_execution(
            self.router_agent.id,
            user_id,
            input.clone(),
        ).await?;

        // Wait for router to complete
        self.execution_service.wait_for_completion(router_execution_id).await?;

        // Get router output (should specify which specialist to use)
        let router_execution = self.execution_service.get_execution(router_execution_id).await?;
        let specialist_name = router_execution.output.unwrap_or_default();

        // Find the specified specialist agent
        let specialist_agent = self.specialist_agents.get(&specialist_name)
            .ok_or_else(|| OrchestratorError::Internal(format!("Specialist agent not found: {}", specialist_name)))?;

        // Execute specialist agent
        let specialist_execution_id = self.execution_service.start_execution(
            specialist_agent.id,
            user_id,
            input.clone(),
        ).await?;

        // Wait for specialist to complete
        self.execution_service.wait_for_completion(specialist_execution_id).await?;

        // Get specialist output
        let specialist_execution = self.execution_service.get_execution(specialist_execution_id).await?;
        let specialist_output = specialist_execution.output.unwrap_or_default();

        // Create input for rephraser agent
        let rephraser_input = format!("Original query: {}\nSpecialist response: {}", input, specialist_output);

        // Execute rephraser agent
        let rephraser_execution_id = self.execution_service.start_execution(
            self.rephraser_agent.id,
            user_id,
            rephraser_input,
        ).await?;

        // Wait for rephraser to complete
        self.execution_service.wait_for_completion(rephraser_execution_id).await?;

        // Get rephraser output
        let rephraser_execution = self.execution_service.get_execution(rephraser_execution_id).await?;
        let output = rephraser_execution.output.unwrap_or_default();

        Ok(output)
    }
}
```

#### 6. Peer-to-Peer Pattern

Agents can hand off queries to one another if they detect that the initial routing was incorrect.

```rust
pub struct PeerToPeerOrchestrator {
    agents: HashMap<String, AgentConfig>,
    execution_service: Arc<ExecutionService>,
    max_hops: usize,
}

impl PeerToPeerOrchestrator {
    pub async fn execute(&self, input: String, user_id: Uuid, initial_agent: String) -> Result<String, OrchestratorError> {
        let mut current_agent_name = initial_agent;
        let mut current_input = input;
        let mut hop_count = 0;

        while hop_count < self.max_hops {
            // Find the current agent
            let current_agent = self.agents.get(&current_agent_name)
                .ok_or_else(|| OrchestratorError::Internal(format!("Agent not found: {}", current_agent_name)))?;

            // Execute current agent
            let execution_id = self.execution_service.start_execution(
                current_agent.id,
                user_id,
                current_input.clone(),
            ).await?;

            // Wait for execution to complete
            self.execution_service.wait_for_completion(execution_id).await?;

            // Get execution result
            let execution = self.execution_service.get_execution(execution_id).await?;

            // Check if agent wants to hand off to another agent
            if let Some(handoff) = execution.metadata.get("handoff") {
                if let Some(next_agent) = handoff.as_str() {
                    if self.agents.contains_key(next_agent) {
                        current_agent_name = next_agent.to_string();
                        current_input = execution.output.unwrap_or_default();
                        hop_count += 1;
                        continue;
                    }
                }
            }

            // No handoff, return the output
            return Ok(execution.output.unwrap_or_default());
        }

        // Max hops reached
        Err(OrchestratorError::Internal("Maximum number of agent hops reached".to_string()))
    }
}
```

#### 7. Adaptive Loop Pattern

This pattern involves iterative refinement, where agents make repeated attempts to improve results until they meet desired criteria.

```rust
pub struct AdaptiveLoopOrchestrator {
    agent: AgentConfig,
    evaluator_agent: AgentConfig,
    execution_service: Arc<ExecutionService>,
    max_iterations: usize,
}

impl AdaptiveLoopOrchestrator {
    pub async fn execute(&self, input: String, user_id: Uuid) -> Result<String, OrchestratorError> {
        let mut current_output = String::new();
        let mut iteration = 0;

        while iteration < self.max_iterations {
            // Execute agent
            let execution_id = self.execution_service.start_execution(
                self.agent.id,
                user_id,
                if iteration == 0 { input.clone() } else { format!("Previous attempt: {}\nInput: {}", current_output, input) },
            ).await?;

            // Wait for execution to complete
            self.execution_service.wait_for_completion(execution_id).await?;

            // Get agent output
            let execution = self.execution_service.get_execution(execution_id).await?;
            current_output = execution.output.unwrap_or_default();

            // Execute evaluator agent
            let evaluator_input = format!("Original query: {}\nAgent response: {}", input, current_output);
            let evaluator_execution_id = self.execution_service.start_execution(
                self.evaluator_agent.id,
                user_id,
                evaluator_input,
            ).await?;

            // Wait for evaluator to complete
            self.execution_service.wait_for_completion(evaluator_execution_id).await?;

            // Get evaluator output
            let evaluator_execution = self.execution_service.get_execution(evaluator_execution_id).await?;
            let evaluation = evaluator_execution.output.unwrap_or_default();

            // Check if the output meets the criteria
            if evaluation.contains("ACCEPTABLE") {
                return Ok(current_output);
            }

            iteration += 1;
        }

        // Max iterations reached
        Ok(current_output)
    }
}
```

### Multi-Agent System Components

To support multi-agent systems, the orchestration layer includes the following components:

1. **Agent Registry**: Manages the registration and discovery of agents
2. **Communication Protocol**: Defines how agents exchange messages
3. **Orchestration Patterns**: Implements the design patterns described above
4. **Memory Management**: Manages shared memory between agents
5. **Monitoring and Telemetry**: Tracks the performance of multi-agent systems

### Best Practices for Multi-Agent Systems

1. **Clear Role Definition**: Each agent should have a clearly defined role and responsibility
2. **Effective Communication**: Establish clear communication protocols between agents
3. **Proper Task Decomposition**: Break down complex tasks into manageable subtasks
4. **Conflict Resolution**: Implement mechanisms to resolve conflicts between agents
5. **Evaluation Framework**: Establish metrics to evaluate the performance of the multi-agent system
6. **Contract-Based Approach**: Use contracts to define the expected inputs, outputs, and behaviors of agents

### Contract-Based Multi-Agent Systems

For complex tasks or high-stakes scenarios, the orchestration layer supports a contract-based approach:

```rust
pub struct AgentContract {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub inputs: Vec<ContractInput>,
    pub outputs: Vec<ContractOutput>,
    pub constraints: Vec<ContractConstraint>,
    pub validation_criteria: Vec<ValidationCriterion>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

pub struct ContractInput {
    pub name: String,
    pub description: String,
    pub schema: serde_json::Value,
    pub required: bool,
}

pub struct ContractOutput {
    pub name: String,
    pub description: String,
    pub schema: serde_json::Value,
}

pub struct ContractConstraint {
    pub description: String,
    pub validation_function: String,
}

pub struct ValidationCriterion {
    pub description: String,
    pub validation_function: String,
}

pub struct ContractBasedOrchestrator {
    contract_registry: Arc<ContractRegistry>,
    agent_registry: Arc<AgentRegistry>,
    execution_service: Arc<ExecutionService>,
}

impl ContractBasedOrchestrator {
    pub async fn execute_contract(&self, contract_id: Uuid, inputs: HashMap<String, serde_json::Value>, user_id: Uuid) -> Result<HashMap<String, serde_json::Value>, OrchestratorError> {
        // Get contract
        let contract = self.contract_registry.get_contract(contract_id).await?;

        // Validate inputs against contract
        self.validate_inputs(&contract, &inputs)?;

        // Find agent that can fulfill the contract
        let agent_id = self.agent_registry.find_agent_for_contract(contract_id).await?;

        // Execute agent
        let execution_id = self.execution_service.start_execution(
            agent_id,
            user_id,
            serde_json::to_string(&inputs)?,
        ).await?;

        // Wait for execution to complete
        self.execution_service.wait_for_completion(execution_id).await?;

        // Get execution result
        let execution = self.execution_service.get_execution(execution_id).await?;
        let output_str = execution.output.unwrap_or_default();

        // Parse output
        let outputs: HashMap<String, serde_json::Value> = serde_json::from_str(&output_str)?;

        // Validate outputs against contract
        self.validate_outputs(&contract, &outputs)?;

        Ok(outputs)
    }

    fn validate_inputs(&self, contract: &AgentContract, inputs: &HashMap<String, serde_json::Value>) -> Result<(), OrchestratorError> {
        // Implementation details
        Ok(())
    }

    fn validate_outputs(&self, contract: &AgentContract, outputs: &HashMap<String, serde_json::Value>) -> Result<(), OrchestratorError> {
        // Implementation details
        Ok(())
    }
}
```

## Implementation Details

### Database Schema

The orchestration layer requires several database tables to store agent configurations, tool definitions, execution history, and more. Here's the recommended schema:

```sql
-- Agents table
CREATE TABLE agents (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    model VARCHAR(255) NOT NULL,
    system_prompt TEXT NOT NULL,
    max_iterations INTEGER NOT NULL DEFAULT 10,
    team_id UUID REFERENCES teams(id),
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);

-- Tools table
CREATE TABLE tools (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    schema JSONB NOT NULL,
    handler_type VARCHAR(50) NOT NULL,
    handler_config JSONB NOT NULL,
    is_public BOOLEAN NOT NULL DEFAULT FALSE,
    team_id UUID REFERENCES teams(id),
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);

-- Agent tools mapping
CREATE TABLE agent_tools (
    agent_id UUID NOT NULL REFERENCES agents(id),
    tool_id UUID NOT NULL REFERENCES tools(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (agent_id, tool_id)
);

-- Executions table
CREATE TABLE executions (
    id UUID PRIMARY KEY,
    agent_id UUID NOT NULL REFERENCES agents(id),
    user_id UUID NOT NULL REFERENCES users(id),
    status VARCHAR(50) NOT NULL,
    input TEXT NOT NULL,
    output TEXT,
    error TEXT,
    started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Execution state table
CREATE TABLE execution_states (
    execution_id UUID NOT NULL REFERENCES executions(id),
    state JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (execution_id)
);

-- Messages table
CREATE TABLE messages (
    id UUID PRIMARY KEY,
    execution_id UUID NOT NULL REFERENCES executions(id),
    role VARCHAR(50) NOT NULL,
    content TEXT,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Tool calls table
CREATE TABLE tool_calls (
    id UUID PRIMARY KEY,
    message_id UUID NOT NULL REFERENCES messages(id),
    tool_id UUID NOT NULL REFERENCES tools(id),
    tool_name VARCHAR(255) NOT NULL,
    arguments JSONB NOT NULL,
    result JSONB,
    error TEXT,
    start_time TIMESTAMPTZ NOT NULL,
    end_time TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Metrics table
CREATE TABLE execution_metrics (
    execution_id UUID NOT NULL REFERENCES executions(id),
    metrics JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (execution_id)
);
```

### API Endpoints

The orchestration layer exposes the following API endpoints:

#### Agent Management

- `POST /api/agents` - Create a new agent
- `GET /api/agents` - List agents
- `GET /api/agents/{id}` - Get agent by ID
- `PUT /api/agents/{id}` - Update agent
- `DELETE /api/agents/{id}` - Delete agent

#### Tool Management

- `POST /api/tools` - Register a new tool
- `GET /api/tools` - List tools
- `GET /api/tools/{id}` - Get tool by ID
- `PUT /api/tools/{id}` - Update tool
- `DELETE /api/tools/{id}` - Delete tool

#### Execution Management

- `POST /api/agents/{id}/execute` - Execute agent
- `GET /api/executions/{id}` - Get execution status
- `GET /api/executions/{id}/messages` - Get execution messages
- `POST /api/executions/{id}/abort` - Abort execution

### Integration with Agent Service

The orchestration layer integrates with the agent service through a client interface:

```rust
pub struct AgentServiceClient {
    base_url: String,
    client: reqwest::Client,
}

impl AgentServiceClient {
    pub fn new(base_url: String) -> Self {
        Self {
            base_url,
            client: reqwest::Client::new(),
        }
    }

    pub async fn register_agent(&self, agent_id: Uuid) -> Result<(), AgentServiceError> {
        // Implementation details
    }

    pub async fn update_agent(&self, agent_id: Uuid) -> Result<(), AgentServiceError> {
        // Implementation details
    }

    pub async fn unregister_agent(&self, agent_id: Uuid) -> Result<(), AgentServiceError> {
        // Implementation details
    }

    pub async fn start_execution(&self, execution_id: Uuid, context: ExecutionContext) -> Result<(), AgentServiceError> {
        // Implementation details
    }

    pub async fn abort_execution(&self, execution_id: Uuid) -> Result<(), AgentServiceError> {
        // Implementation details
    }
}
```

## Example Usage

### Creating and Executing an Agent

```rust
// Create agent manager
let agent_manager = AgentManager::new(db_pool.clone(), agent_service_client.clone());

// Create tool registry
let tool_registry = ToolRegistry::new(db_pool.clone());

// Create execution service
let execution_service = ExecutionService::new(
    db_pool.clone(),
    agent_service_client.clone(),
    Arc::new(tool_registry),
);

// Create an agent
let agent_config = AgentConfig {
    id: Uuid::new_v4(),
    name: "Customer Support Agent".to_string(),
    description: "Handles customer support inquiries".to_string(),
    model: "gemini-pro".to_string(),
    tools: vec![],  // Will be populated later
    system_prompt: "You are a helpful customer support agent...".to_string(),
    max_iterations: 10,
    team_id: Some(team_id),
    created_by: user_id,
    created_at: Utc::now(),
    updated_at: Utc::now(),
};

let agent_id = agent_manager.create_agent(agent_config).await?;

// Register a tool
let tool_config = ToolConfig {
    id: Uuid::new_v4(),
    name: "get_order_status".to_string(),
    description: "Get the status of an order".to_string(),
    schema: serde_json::json!({
        "type": "object",
        "properties": {
            "order_id": {
                "type": "string",
                "description": "The order ID"
            }
        },
        "required": ["order_id"]
    }),
    handler_type: ToolHandlerType::Http,
    handler_config: serde_json::json!({
        "url": "https://api.example.com/orders/{order_id}",
        "method": "GET",
        "headers": {
            "Authorization": "Bearer {{API_KEY}}"
        }
    }),
    created_at: Utc::now(),
    updated_at: Utc::now(),
};

let tool_id = tool_registry.register_tool(tool_config).await?;

// Add tool to agent
agent_manager.add_tool_to_agent(agent_id, tool_id).await?;

// Execute agent
let execution_id = execution_service.start_execution(
    agent_id,
    user_id,
    "What's the status of my order #12345?".to_string(),
).await?;

// Get execution status
let status = execution_service.get_execution_status(execution_id).await?;

// Get execution messages
let state_manager = StateManager::new(db_pool.clone());
let messages = state_manager.get_history(execution_id).await?;
```

## Testing Strategy

A comprehensive testing strategy for the orchestration layer should include:

### Unit Tests

Test individual components in isolation:

```rust
#[tokio::test]
async fn test_agent_manager_create_agent() {
    // Setup mock database and agent service client
    let db_pool = setup_test_db().await;
    let agent_service_client = MockAgentServiceClient::new();

    // Create agent manager
    let agent_manager = AgentManager::new(db_pool.clone(), Arc::new(agent_service_client));

    // Create agent config
    let agent_config = AgentConfig {
        // Config details
    };

    // Call create_agent
    let result = agent_manager.create_agent(agent_config).await;

    // Assert result
    assert!(result.is_ok());
    let agent_id = result.unwrap();

    // Verify agent was stored in database
    let stored_agent = get_agent_from_db(db_pool, agent_id).await;
    assert!(stored_agent.is_some());

    // Verify agent service client was called
    assert!(agent_service_client.register_agent_called_with(agent_id));
}
```

### Integration Tests

Test the interaction between components:

```rust
#[tokio::test]
async fn test_agent_execution_flow() {
    // Setup test environment
    let (db_pool, agent_service_client, agent_manager, tool_registry, execution_service) =
        setup_test_environment().await;

    // Create agent and tool
    let agent_id = create_test_agent(agent_manager).await;
    let tool_id = register_test_tool(tool_registry).await;

    // Add tool to agent
    agent_manager.add_tool_to_agent(agent_id, tool_id).await.unwrap();

    // Execute agent
    let execution_id = execution_service.start_execution(
        agent_id,
        test_user_id(),
        "Test input".to_string(),
    ).await.unwrap();

    // Wait for execution to complete
    wait_for_execution_completion(execution_service, execution_id).await;

    // Verify execution status
    let status = execution_service.get_execution_status(execution_id).await.unwrap();
    assert_eq!(status, ExecutionStatus::Completed);

    // Verify messages
    let state_manager = StateManager::new(db_pool.clone());
    let messages = state_manager.get_history(execution_id).await.unwrap();
    assert!(!messages.is_empty());

    // Verify tool calls
    let tool_calls = get_tool_calls_for_execution(db_pool, execution_id).await;
    assert!(!tool_calls.is_empty());
}
```

### End-to-End Tests

Test complete agent workflows:

```rust
#[tokio::test]
async fn test_customer_support_agent_workflow() {
    // Setup real environment (or realistic mocks)
    let (agent_manager, tool_registry, execution_service) = setup_real_environment().await;

    // Create customer support agent with necessary tools
    let agent_id = create_customer_support_agent(agent_manager, tool_registry).await;

    // Execute agent with a customer inquiry
    let execution_id = execution_service.start_execution(
        agent_id,
        test_user_id(),
        "I haven't received my order #54321 yet. It's been 5 days.".to_string(),
    ).await.unwrap();

    // Wait for execution to complete
    wait_for_execution_completion(execution_service, execution_id).await;

    // Verify the agent's response
    let state_manager = StateManager::new(db_pool.clone());
    let messages = state_manager.get_history(execution_id).await.unwrap();

    // Find the last assistant message
    let last_assistant_message = messages.iter()
        .filter(|m| m.role == MessageRole::Assistant)
        .last()
        .unwrap();

    // Verify the response contains expected information
    assert!(last_assistant_message.content.contains("shipping delay"));
    assert!(last_assistant_message.content.contains("expected delivery date"));
}
```
