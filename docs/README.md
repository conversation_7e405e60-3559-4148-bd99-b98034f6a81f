# Saday Agent Documentation

## Overview

Saday Agent is an AI agent builder and orchestration platform that allows users to create, manage, and deploy AI agents for various tasks. The platform is built on a Rust backend with React-based web and mobile frontends.

## Architecture

### Backend
- **Authentication**: Passwordless OTP-based authentication system
- **Database**: PostgreSQL with SQLx for type-safe queries
- **API**: RESTful API with WebSocket support
- **Agent System**: Agent orchestration layer based on AgentAI

### Frontend
- **Web**: React-based web application
- **Mobile**: React Native mobile application
- **Shared UI**: Cross-platform UI component library (ApatiteUI)
- **Shared Logic**: Common authentication and API interaction logic

## Components

1. [Authentication System](./authentication.md)
2. [Agent System](./agent-system.md)
3. [Chat Interface](./chat-interface.md)
4. [Team Management](./team-management.md)
5. [UI Library](./ui-library.md)
6. [API Reference](./api-reference.md)

## Development Setup

1. [Environment Setup](./environment-setup.md)
2. [Local Development](./local-development.md)
3. [Testing](./testing.md)
4. [Deployment](./deployment.md)