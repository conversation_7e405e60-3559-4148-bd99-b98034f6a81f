#!/bin/bash

# Colors for terminal output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message "${BLUE}" "=== Organization API Test ==="

# Ask for the token
print_message "${BLUE}" "Please paste your JWT token:"
read -p "Token: " TOKEN

if [ -z "$TOKEN" ]; then
    print_message "${RED}" "No token provided. Exiting."
    exit 1
fi

# API URL
API_URL="http://localhost:8001/api"

# Test 1: Create Organization
print_message "${YELLOW}" "Test 1: Creating a new organization..."
ORG_RESPONSE=$(curl -s -X POST "${API_URL}/organizations/" \
    -H "Authorization: Bearer ${TOKEN}" \
    -H "Content-Type: application/json" \
    --cookie "token=${TOKEN}" \
    -d '{
        "name": "Test Organization",
        "description": "This is a test organization",
        "domain": "test.com",
        "settings": {
            "color": "#FF5733",
            "logo": "https://example.com/logo.png"
        }
    }')

print_message "${GREEN}" "Response: $ORG_RESPONSE"

# Extract organization ID from response
ORG_ID=$(echo $ORG_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)

if [ -z "$ORG_ID" ]; then
    print_message "${RED}" "Failed to extract organization ID. Skipping remaining tests."
else
    print_message "${GREEN}" "Organization created with ID: $ORG_ID"

    # Test 2: Get Organizations
    print_message "${YELLOW}" "Test 2: Getting user organizations..."
    ORGS_RESPONSE=$(curl -s -X GET "${API_URL}/organizations/" \
        -H "Authorization: Bearer ${TOKEN}" \
        --cookie "token=${TOKEN}")
    print_message "${GREEN}" "Response: $ORGS_RESPONSE"

    # Test 3: Get Organization by ID
    print_message "${YELLOW}" "Test 3: Getting organization by ID..."
    ORG_DETAIL_RESPONSE=$(curl -s -X GET "${API_URL}/organizations/$ORG_ID" \
        -H "Authorization: Bearer ${TOKEN}" \
        --cookie "token=${TOKEN}")
    print_message "${GREEN}" "Response: $ORG_DETAIL_RESPONSE"

    # Test 4: Update Organization
    print_message "${YELLOW}" "Test 4: Updating organization..."
    UPDATE_RESPONSE=$(curl -s -X PUT "${API_URL}/organizations/$ORG_ID" \
        -H "Authorization: Bearer ${TOKEN}" \
        -H "Content-Type: application/json" \
        --cookie "token=${TOKEN}" \
        -d '{
            "name": "Updated Test Organization",
            "description": "This is an updated test organization",
            "domain": "updated-test.com",
            "settings": {
                "color": "#33FF57",
                "logo": "https://example.com/updated-logo.png"
            }
        }')
    print_message "${GREEN}" "Response: $UPDATE_RESPONSE"

    # Test 5: Get Organization Members
    print_message "${YELLOW}" "Test 5: Getting organization members..."
    MEMBERS_RESPONSE=$(curl -s -X GET "${API_URL}/organizations/$ORG_ID/members" \
        -H "Authorization: Bearer ${TOKEN}" \
        --cookie "token=${TOKEN}")
    print_message "${GREEN}" "Response: $MEMBERS_RESPONSE"

    # Test 6: Invite Member
    print_message "${YELLOW}" "Test 6: Inviting a new member..."
    INVITE_RESPONSE=$(curl -s -X POST "${API_URL}/organizations/$ORG_ID/members" \
        -H "Authorization: Bearer ${TOKEN}" \
        -H "Content-Type: application/json" \
        --cookie "token=${TOKEN}" \
        -d '{
            "email": "<EMAIL>",
            "role": "orgeditor"
        }')
    print_message "${GREEN}" "Response: $INVITE_RESPONSE"

    # Test 7: Get Organization Invitations
    print_message "${YELLOW}" "Test 7: Getting organization invitations..."
    INVITATIONS_RESPONSE=$(curl -s -X GET "${API_URL}/organizations/$ORG_ID/invitations" \
        -H "Authorization: Bearer ${TOKEN}" \
        --cookie "token=${TOKEN}")
    print_message "${GREEN}" "Response: $INVITATIONS_RESPONSE"

    # Test 8: Get User Invitations
    print_message "${YELLOW}" "Test 8: Getting user invitations..."
    USER_INVITATIONS_RESPONSE=$(curl -s -X GET "${API_URL}/organizations/invitations" \
        -H "Authorization: Bearer ${TOKEN}" \
        --cookie "token=${TOKEN}")
    print_message "${GREEN}" "Response: $USER_INVITATIONS_RESPONSE"

    # Test 9: Set Default Organization
    print_message "${YELLOW}" "Test 9: Setting default organization..."
    DEFAULT_RESPONSE=$(curl -s -X PUT "${API_URL}/organizations/default/$ORG_ID" \
        -H "Authorization: Bearer ${TOKEN}" \
        --cookie "token=${TOKEN}")
    print_message "${GREEN}" "Response: $DEFAULT_RESPONSE"

    # Test 10: Delete Organization
    print_message "${YELLOW}" "Test 10: Deleting organization..."
    DELETE_RESPONSE=$(curl -s -X DELETE "${API_URL}/organizations/$ORG_ID" \
        -H "Authorization: Bearer ${TOKEN}" \
        --cookie "token=${TOKEN}")
    print_message "${GREEN}" "Response: $DELETE_RESPONSE"
fi

print_message "${BLUE}" "=== Test Completed ==="
