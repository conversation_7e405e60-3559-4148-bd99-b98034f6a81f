#!/bin/bash

# Colors for terminal output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message "${BLUE}" "=== Authentication Debug Tool ==="

# API URL
API_URL="http://localhost:8001/api"

# Step 1: Request OTP
print_message "${YELLOW}" "Step 1: Requesting OTP..."
read -p "Enter your email: " EMAIL

if [ -z "$EMAIL" ]; then
    print_message "${RED}" "No email provided. Exiting."
    exit 1
fi

OTP_RESPONSE=$(curl -s -X POST "${API_URL}/auth/request-otp" \
    -H "Content-Type: application/json" \
    -d "{\"email\": \"$EMAIL\"}")

print_message "${GREEN}" "Response: $OTP_RESPONSE"

# Extract user type
USER_TYPE=$(echo $OTP_RESPONSE | grep -o '"userType":"[^"]*' | cut -d'"' -f4)
print_message "${YELLOW}" "User type: $USER_TYPE"

# Step 2: Verify OTP
print_message "${YELLOW}" "Step 2: Verifying OTP..."
read -p "Enter the OTP code sent to your email: " OTP_CODE

if [ -z "$OTP_CODE" ]; then
    print_message "${RED}" "No OTP code provided. Exiting."
    exit 1
fi

# For new users, ask for a name
if [ "$USER_TYPE" == "New" ]; then
    read -p "You are a new user. Enter your name: " NAME
    VERIFY_PAYLOAD="{\"email\": \"$EMAIL\", \"otp_code\": \"$OTP_CODE\", \"name\": \"$NAME\", \"profile_data\": {}}"
else
    VERIFY_PAYLOAD="{\"email\": \"$EMAIL\", \"otp_code\": \"$OTP_CODE\"}"
fi

VERIFY_RESPONSE=$(curl -s -X POST "${API_URL}/auth/verify-otp" \
    -H "Content-Type: application/json" \
    -d "$VERIFY_PAYLOAD" \
    -v 2>&1)

# Extract token from response
TOKEN=$(echo "$VERIFY_RESPONSE" | grep -o '"token":"[^"]*' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    print_message "${RED}" "Failed to extract token from response."
    print_message "${YELLOW}" "Full response: $VERIFY_RESPONSE"
    exit 1
fi

print_message "${GREEN}" "Authentication successful!"
print_message "${YELLOW}" "Your JWT token: $TOKEN"

# Step 3: Test token with /users/me endpoint
print_message "${YELLOW}" "Step 3: Testing token with /users/me endpoint..."
ME_RESPONSE=$(curl -s -X GET "${API_URL}/users/me" \
    -H "Authorization: Bearer ${TOKEN}" \
    --cookie "token=${TOKEN}")

print_message "${GREEN}" "Response: $ME_RESPONSE"

# Step 4: Test token with /organizations endpoint
print_message "${YELLOW}" "Step 4: Testing token with /organizations endpoint..."
ORG_RESPONSE=$(curl -s -X GET "${API_URL}/organizations" \
    -H "Authorization: Bearer ${TOKEN}" \
    --cookie "token=${TOKEN}")

print_message "${GREEN}" "Response: $ORG_RESPONSE"

print_message "${BLUE}" "=== Debug Completed ==="
print_message "${YELLOW}" "You can use this token for testing the organization API:"
echo "$TOKEN"
